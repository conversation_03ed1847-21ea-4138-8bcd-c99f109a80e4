<template>
  <div ref="worldMap" class="chart" />
</template>
  
<script>
import * as echarts from 'echarts'
import 'echarts-gl';
export default {
  props: {
    chartData: {
      type: Array,
      default: () => []
    },
    showLoading: {
      type: Boolean,
      default: true
    },
  },
  data() {
    return {
      chart: null,
      timer: null
    }
  },
  watch: {
    chartData() {
      this.$nextTick(() => {
        this.initChart()
      })
    }
  },
  before<PERSON><PERSON><PERSON>() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  mounted() {
    // this.initChart()
    
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.worldMap)
          
      // this.chart.showLoading();
    //    let dataArr = [
    //  {
    //     name: '中国',
    //     value: 15,
    //     point: [116.46, 39.92, 0],
    // }, {
    //     name: '印度',
    //     value: 200,
    //     point: [78.96288, 20.593684, 0],
    // }, {
    //     name: '意大利',
    //     value: 10,
    //     point: [12.56738, 41.87194, 0],
    // }, {
    //     name: '新西兰',
    //     value: 20,
    //     point: [174.885971, -40.900557, 0],
    // }, {
    //     name: '英国',
    //     value: 100,
    //     point: [-3.435973, 55.378051, 0],
    // }, {
    //     name: '德国',
    //     value: 200,
    //     point: [10.451526, 51.165691, 0],
    // }, {
    //     name: '美国',
    //     value: 2200,
    //     point: [-95.712891, 37.09024, 0],
    // }, {
    //     name: '日本',
    //     value: 2500,
    //     point: [138.252924, 36.204824, 0],
    // }]
        let dataArr = this.chartData
      const data = []
      const colors = ['#1D80DA', '#02F4FF', '#E3BC2D', '#FF6632', '#A7FFB0', '#8A01E1', '#f462d0 ']
      for (var i = 0; i < dataArr?.length; i++) {
      const currentColor = colors[i % colors.length];
        data.push(
          {
            name: dataArr[i].areaName, // 是否显示左上角图例
            type: 'scatter3D',
            coordinateSystem: 'globe',
            blendMode: 'lighter',
            symbolSize: 10, // 点位大小
            itemStyle: {
                color: currentColor, // 各个点位的颜色设置
                opacity: 1, // 透明度
                borderWidth: 1, // 边框宽度
                borderColor: 'rgba(255,255,255,0.8)' //rgba(180, 31, 107, 0.8)
            },
            label: {
                show: true, // 是否显示字体
                position: 'left', // 字体位置。top、left、right、bottom
                formatter: dataArr[i].areaName + '-' + dataArr[i].issueArticleNum, // 具体显示的值
                textStyle: {
                    color: '#fff', // 字体颜色
                    borderWidth: 0, // 字体边框宽度
                    borderColor: '#fff', // 字体边框颜色
                    fontFamily: 'sans-serif', // 字体格式
                    fontSize: '0.14rem', // 字体大小
                    fontWeight: 700 // 字体加粗
                }
            },
            data: [dataArr[i].pointValue] // 数据来源
         
          }
        )
      }

      const option = {
        // 图例设置
        legend: {
            type: 'scroll',
            icon: 'circle', // 图例形状
            selectedMode: 'multiple', // single multiple
            x: 'right',
            y: 'bottom',
            data: dataArr.map(item => item.areaName), // 数据来源
            formatter: function (name) {
              const foundItem = dataArr.find(item => item.areaName === name);
              if (foundItem) {
          return `${foundItem.areaName}-${foundItem.issueArticleNum}`;
              }
              return '';
            },
            orient: 'vertical', // 排列方式，vertical:垂直排列
            textStyle: {
              color: '#fff', // 文字颜色
            },
            pageIconColor: '#fff', // 分页图标颜色
            pageTextStyle: {
              color: '#fff' // 分页文字颜色
            },
            pageButtonItemGap: 16, // 分页按钮之间的间隔
        },
        tooltip: {
            show: true,
            trigger: 'item',
            enterable: true,
            renderMode: 'html',
            appendTo: true,
            position: (point, params, dom, rect, size) => {
              //   console.log(point, params, dom, rect, size, 'point, params, dom, rect, size')
              return [point[0] - 70, point[1] - 72,0]
            },
            formatter: function (params) {
              const foundItem = dataArr.find(item => item.areaName === params.name);
                if (foundItem) {
                  return `${params.name} : ${foundItem.issueArticleNum}`;
                }
                return params.name;
            }
        },
        // 地球背景色
        // backgroundColor: '#058198',
        // 地球参数设置
        globe: {
            baseTexture: require('@/assets/images/map-img.png'), // 地球表面覆盖的图片,可以替换成自己想要的图片
            // baseTexture: '//img.isqqw.com/profile/upload/2024/08/14/457367a4-16b6-453f-9894-baa2eb3e9503.png', // 地球表面覆盖的图片,可以替换成自己想要的图片
            shading: 'color', // 地球中三维图形的着色效果
            viewControl: {
                autoRotate: true, // 是否开启视角绕物体的自动旋转查看
                autoRotateSpeed: 3, //物体自转的速度,单位为角度 / 秒，默认为10 ，也就是36秒转一圈。
                autoRotateAfterStill: 2, // 在鼠标静止操作后恢复自动旋转的时间间隔,默认 3s
                rotateSensitivity: 2, // 旋转操作的灵敏度，值越大越灵敏.设置为0后无法旋转。[1, 0]只能横向旋转.[0, 1]只能纵向旋转
                targetCoord: [116.46, 39.92], // 定位到北京
                maxDistance: 150,
                minDistance: 150
            }
               
        },
        // 地球文字显示信息配置
        series: data
      }
     
      if (!this.showLoading) {
        this.chart.hideLoading()
      }
      this.chart.setOption(option)

    }

  }
}
</script>
<style lang="scss" scoped>
  .chart {
    width: 100%;
    height: 100%;
  }
</style>
  
