<template>
  <div class="template-bltw">
    <div class="post-block">
      <img :src="getAvatar(avatar)" alt="用户头像">
      <div class="main-content">
        <div>
          <div class="user-name">
            <span class="text">{{ nickName }}</span>
            <img src="@/assets/images/simulatedVS/vip7.png" alt="">
            <span style="flex: 1;"></span>
            <img v-if="teamType==2" src="@/assets/images/simulatedVS/teamPointBlue.png" alt="">
            <img v-if="teamType==1" src="@/assets/images/simulatedVS/teamPointRed.png" alt="">
          </div>
          <div class="createTime">{{createTime}}</div>
          <div class="content">{{ content }}</div>
        </div>
        <template v-if="fileList && fileList.length>0">
          <div class="file-list" v-for="itemInside in fileList" :key="itemInside.key"
            :style="{'display': isVideoType(itemInside.type)?'block':'inline-block'}">
            <!-- 图片类型 -->
            <template v-if="isImageType(itemInside.type)">
              <el-image class="img-preview" style="width: 100px; height: 100px" :src="buildFileUrl(itemInside.url)"
                :preview-src-list="[buildFileUrl(itemInside.url)]">
              </el-image>
            </template>

            <!-- 视频类型 -->
            <template v-else-if="isVideoType(itemInside.type)">
              <video style="width: 100%; height: auto;max-height: 20em;" controls>
                <source :src="buildFileUrl(itemInside.url)" :type="getVideoMimeType(itemInside.type)">
                您的浏览器不支持 video 标签
              </video>
            </template>

            <!-- 其他类型 -->
            <template v-else>
              <div class="file-preview">
                <i class="el-icon-document"></i>
                <span>{{ itemInside.name }}</span>
              </div>
            </template>
          </div>
        </template>
        <div class="bottom-icon">
          <div class="icon-item">
            <img src="@/assets/images/simulatedVS/shareIcon.png" alt="">
            <span class="text" style="filter: blur(0.15em);">99999</span>
          </div>
          <div class="icon-item">
            <img src="@/assets/images/simulatedVS/commentIcon.png" alt="" @click="$emit('comment', item)">
            <span class="text">{{ actualCommentCount }}</span>
          </div>
          <div class="icon-item">
            <img :src="require(isLike?'@/assets/images/simulatedVS/likedIcon.png':'@/assets/images/simulatedVS/likeIcon.png')"
              @click="$emit('like', item)">
            <span class="text">{{ likeCount }}</span>
          </div>
        </div>
      </div>
    </div>
    <div v-if="showCommentBlock" class="comment-block-top">
      <div class="top-left">评论&nbsp;{{ actualCommentCount }}</div>
      <div class="sort-buttons">
        <button
          :class="['sort-btn', { active: currentSortBy === 'createdTime' }]"
          @click="changeSortBy('createdTime')"
        >
          按时间
        </button>
        <button
          :class="['sort-btn', { active: currentSortBy === 'likeCount' }]"
          @click="changeSortBy('likeCount')"
        >
          按点赞
        </button>
      </div>
    </div>
    <!-- 评论区 -->
    <div v-if="showCommentBlock" class="comment-block">
      <!-- 加载状态 -->
      <div v-if="commentLoading" class="loading-container">
        <i class="el-icon-loading"></i>
        <span>加载中...</span>
      </div>

      <!-- v-for 循环评论 -->
      <div class="comment-item" v-for="(replyItem,replyIndex) in displayComments" :key="`comment-${replyItem.commentReplyId || replyItem.id || replyIndex}-${currentSortBy}`">
        <img :src="getAvatar(replyItem.avatar)" alt="用户头像">
        <div class="main-content">
          <div>
            <div class="user-name">{{ replyItem.nickName }}</div>
            <div class="content">:&nbsp;{{ replyItem.content }}</div>
          </div>

          <template v-if="replyItem.fileList && replyItem.fileList.length>0">
            <div class="file-list" v-for="itemInside in replyItem.fileList" :key="itemInside.key"
              :style="{'display': isVideoType(itemInside.type)?'block':'inline-block'}">
              <!-- 图片类型 -->
              <template v-if="isImageType(itemInside.type)">
                <el-image class="img-preview" style="width: 100px; height: 100px" :src="buildFileUrl(itemInside.url)"
                  :preview-src-list="[buildFileUrl(itemInside.url)]">
                </el-image>
              </template>

              <!-- 视频类型 -->
              <template v-else-if="isVideoType(itemInside.type)">
                <video style="width: 100%; height: auto;max-height: 20em;" controls>
                  <source :src="buildFileUrl(itemInside.url)" :type="getVideoMimeType(itemInside.type)">
                  您的浏览器不支持 video 标签
                </video>
              </template>

              <!-- 其他类型 -->
              <template v-else>
                <div class="file-preview">
                  <i class="el-icon-document"></i>
                  <span>{{ itemInside.name }}</span>
                </div>
              </template>
            </div>
          </template>
          <div class="comment-bottom">
            <div class="createTime">{{replyItem.createdTime}}</div>
            <div class="option-icon">
              <img :src="require(replyItem.isLike?'@/assets/images/simulatedVS/likedIcon.png':'@/assets/images/simulatedVS/likeIcon.png')"
                alt="" @click="$emit('like', replyItem)">
              <span class="text">{{ replyItem.likeCount||0 }}</span>
            </div>
          </div>
        </div>

        <img class="team-point" v-if="replyItem.teamType==2" src="@/assets/images/simulatedVS/teamPointBlue.png" alt="">
        <img class="team-point" v-else-if="replyItem.teamType==1" src="@/assets/images/simulatedVS/teamPointRed.png" alt="">
        <div class="team-point" v-else></div>
      </div>

      <!-- 滚动加载提示 -->
      <div v-if="hasMoreComments && commentLoading" class="scroll-loading-tip">
        <i class="el-icon-loading"></i>
        <span>正在加载更多评论...</span>
      </div>

      <!-- 没有更多评论提示 -->
      <div v-if="!hasMoreComments && displayComments.length > 0" class="no-more-tip">
        没有更多评论了
      </div>
    </div>
  </div>
</template>

<script>
import { commentReplyQueryApi } from "@/api/simulatedVS/index.js";

export default {
  name: 'PostTemplate',
  data() {
    return {
      // 分页查询的评论数据
      paginatedComments: [],
      // 当前排序方式：createdTime(时间) 或 likeCount(点赞数)
      currentSortBy: 'createdTime',
      // 游标分页相关
      nextCursor: null,
      hasMoreComments: false,
      commentLoading: false,
      // 总评论数
      totalCommentCount: 0,
      // 是否已初始化查询
      hasInitialized: false,
      // 滚动监听相关
      scrollTimer: null,
      isScrollLoading: false,
      // 用于检测新评论的上一次回复ID列表
      lastReplyIds: []
    }
  },
  computed: {
    // 实际评论数量（优先显示真实数量）
    actualCommentCount() {
      if (this.paginatedComments.length > 0) {
        // 使用分页数据时，优先使用API返回的总数，如果没有则使用当前显示的数量
        return this.totalCommentCount > 0 ? this.totalCommentCount : this.paginatedComments.length;
      } else if (this.drillCommentReplyRes?.length > 0) {
        // 使用旧数据时，直接使用数组长度
        return this.drillCommentReplyRes.length;
      }
      // 都没有数据时，使用存储的总数
      return this.totalCommentCount;
    },
    // 显示评论区的条件
    showCommentBlock() {
      return this.drillCommentReplyRes?.length > 0 || this.paginatedComments.length > 0 || this.totalCommentCount > 0;
    },
    // 显示的评论列表（优先显示分页查询的数据，兼容旧数据）
    displayComments() {
      if (this.paginatedComments.length > 0) {
        // 确保分页数据也进行去重
        return this.deduplicateComments(this.paginatedComments);
      }
      // 兼容旧版本数据，按当前排序方式排序
      return this.sortedComments;
    },
    // 按当前排序方式排序评论（兼容旧版本）
    sortedComments() {
      if (!this.drillCommentReplyRes || this.drillCommentReplyRes.length === 0) {
        return [];
      }

      // 创建数组副本并按当前排序方式排序
      return [...this.drillCommentReplyRes].sort((a, b) => {
        if (this.currentSortBy === 'createdTime') {
          // 按时间降序排列（最新的在前面）
          const timeA = new Date(a.createdTime || 0).getTime();
          const timeB = new Date(b.createdTime || 0).getTime();
          return timeB - timeA; // 降序：新的在前
        } else {
          // 按点赞数降序排列（点赞多的在前面）
          const likeCountA = a.likeCount || 0;
          const likeCountB = b.likeCount || 0;
          return likeCountB - likeCountA; // 降序：点赞多的在前
        }
      });
    }
  },
  watch: {
    // 监听item变化，当渲染到这个评论时进行初始化查询
    item: {
      immediate: true,
      handler(newItem, oldItem) {
        // 如果commentId发生变化，重置初始化状态
        if (oldItem?.commentId !== newItem?.commentId) {
          this.hasInitialized = false;
          this.paginatedComments = [];
          this.nextCursor = null;
          this.hasMoreComments = false;
        }

        // 延迟检查，确保其他props也已更新
        this.$nextTick(() => {
          this.checkAndInitialize();
        });
      }
    },
    // 监听drillTaskId变化，重新初始化
    drillTaskId: {
      immediate: true,
      handler(newVal, oldVal) {
        // 如果drillTaskId发生变化，重置初始化状态
        if (oldVal !== newVal && oldVal !== undefined) {
          this.hasInitialized = false;
          this.paginatedComments = [];
          this.nextCursor = null;
          this.hasMoreComments = false;
        }

        // 延迟检查，确保其他props也已更新
        this.$nextTick(() => {
          this.checkAndInitialize();
        });
      }
    },
    // 监听drillCommentReplyRes变化，处理新评论添加
    drillCommentReplyRes: {
      immediate: true,
      deep: true, // 深度监听数组变化
      handler(newVal, oldVal) {
        const currentIds = newVal?.map(item => item.commentReplyId) || [];

        console.log('🔍 drillCommentReplyRes变化检测:', {
          commentId: this.item?.commentId,
          newLength: newVal?.length || 0,
          oldLength: oldVal?.length || 0,
          newFirstId: newVal?.[0]?.commentReplyId,
          oldFirstId: oldVal?.[0]?.commentReplyId,
          paginatedCommentsLength: this.paginatedComments.length,
          currentSortBy: this.currentSortBy,
          lastReplyIds: this.lastReplyIds,
          currentIds: currentIds
        });

        // 更新总数（兼容旧版本）
        if (newVal && newVal.length > 0) {
          // 如果没有分页数据，使用旧数据的长度作为总数
          if (this.paginatedComments.length === 0) {
            this.totalCommentCount = newVal.length;
          }
        }

        // 检测新评论：比较当前ID列表和上次记录的ID列表
        if (newVal && newVal.length > 0) {
          // 找出新增的评论ID
          const newCommentIds = currentIds.filter(id => !this.lastReplyIds.includes(id));

          if (newCommentIds.length > 0) {
            console.log('🆕 检测到新评论ID:', newCommentIds);

            // 找出对应的新评论对象
            const newComments = newVal.filter(comment =>
              newCommentIds.includes(comment.commentReplyId)
            );

            if (newComments.length > 0) {
              console.log('✅ 找到新评论对象，调用handleNewCommentAdded');
              this.handleNewCommentAdded(newComments);
            }
          }
        }

        // 更新记录的ID列表
        this.lastReplyIds = [...currentIds];
      }
    },

    // 监听分页评论数据变化，确保滚动监听器绑定
    paginatedComments: {
      handler(newVal, oldVal) {
        if (newVal && newVal.length > 0 && (!oldVal || oldVal.length === 0)) {
          this.$nextTick(() => {
            this.bindScrollListener();
          });
        }
      }
    },

    // 监听显示评论区的条件变化
    showCommentBlock: {
      handler(newVal, oldVal) {
        if (newVal && !oldVal) {
          this.$nextTick(() => {
            this.bindScrollListener();
          });
        }
      }
    }
  },
  props: {
    item: {
      type: Object,
      default: () => ({})
    },
    avatar: {
      type: String,
      default: ''
    },
    nickName: {
      type: String,
      default: ''
    },
    createTime: {
      type: String,
      default: ''
    },
    content: {
      type: String,
      default: ''
    },
    teamType: {
      type: [Number, String],
      default: 0
    },
    fileList: {
      type: Array,
      default: () => []
    },
    drillCommentReplyRes: {
      type: Array,
      default: () => []
    },
    isLike: {
      type: Boolean,
      default: false
    },
    likeCount: {
      type: [Number, String],
      default: 0
    },
    imageTypes: {
      type: Array,
      default: () => ['png', 'jpeg', 'jpg']
    },
    videoTypes: {
      type: Array,
      default: () => ['mp4', 'avi', 'rmvb', 'mov', 'mkv']
    },
    // 新增props用于分页查询
    drillTaskId: {
      type: [String, Number],
      default: ''
    },
    processStageId: {
      type: [String, Number],
      default: ''
    }
  },
  mounted() {
    // 延迟检查初始化条件，确保所有props都已传递
    this.$nextTick(() => {
      setTimeout(() => {
        this.checkAndInitialize();
      }, 100);
    });

    // 监听点赞更新事件
    this.$root.$on('comment-like-update', this.handleLikeUpdate);
    console.log('📡 PostTemplate组件已注册点赞更新事件监听器:', {
      commentId: this.item?.commentId,
      componentKey: this.$vnode?.key,
      isInAmplifyDialog: this.$el?.closest('.amplify-dialog') !== null
    });
  },
  beforeDestroy() {
    // 清理滚动监听
    const commentBlock = this.$el?.querySelector('.comment-block');
    if (commentBlock && commentBlock.dataset.scrollListenerBound) {
      commentBlock.removeEventListener('scroll', this.handleCommentScroll);
      delete commentBlock.dataset.scrollListenerBound;
    }
    // 清理定时器
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer);
      this.scrollTimer = null;
    }
    // 清理事件监听
    this.$root.$off('comment-like-update', this.handleLikeUpdate);
  },
  methods: {
    // 判断是否是当前允许的图片类型
    isImageType(fileType) {
      return this.imageTypes.includes(fileType)
    },
    // 判断是否是当前允许的视频类型
    isVideoType(fileType) {
      return this.videoTypes.includes(fileType)
    },
    // 获取视频MIME类型
    getVideoMimeType(ext) {
      const types = {
        mp4: 'video/mp4',
        avi: 'video/x-msvideo',
        mov: 'video/quicktime',
        mkv: 'video/x-matroska',
        rmvb: 'application/vnd.rn-realmedia-vbr'
      }
      return types[ext] || 'video/*'
    },
    buildFileUrl(url) {
      return process.env.VUE_APP_BASE_API + url;
    },
    // 添加头像方法
    getAvatar(avatar) {
      return avatar ? process.env.VUE_APP_BASE_API + avatar : require("@/assets/images/profile.jpg");
    },

    // 检查并初始化
    checkAndInitialize() {
      // 如果有旧数据，不需要初始化新的分页查询，但仍需要绑定滚动监听器
      if (this.drillCommentReplyRes?.length > 0) {
        this.bindScrollListener();
        return;
      }

      // 检查是否满足初始化条件
      if (this.item?.commentId && this.drillTaskId && !this.hasInitialized) {
        this.initializeCommentQuery();
      }
    },

    // 绑定滚动监听器
    bindScrollListener() {
      // 等待DOM更新
      this.$nextTick(() => {
        const commentBlock = this.$el.querySelector('.comment-block');

        if (commentBlock) {
          // 检查是否已经绑定过
          if (!commentBlock.dataset.scrollListenerBound) {
            commentBlock.addEventListener('scroll', this.handleCommentScroll, { passive: true });
            commentBlock.dataset.scrollListenerBound = 'true';
          }
        } else {
          // 如果还没找到，延迟重试
          setTimeout(() => {
            this.bindScrollListener();
          }, 500);
        }
      });
    },

    // 初始化评论查询
    async initializeCommentQuery() {
      if (!this.item?.commentId || !this.drillTaskId) {
        return;
      }

      if (this.hasInitialized) {
        return;
      }

      this.hasInitialized = true;
      await this.queryCommentReplies(true);

      // 查询完成后绑定滚动监听器
      this.bindScrollListener();
    },

    // 查询评论回复
    async queryCommentReplies(isReset = false) {
      if (this.commentLoading) return;

      try {
        this.commentLoading = true;

        const params = {
          drillTaskId: this.drillTaskId,
          processStageId: this.processStageId,
          commentId: this.item.commentId,
          cursor: isReset ? null : this.nextCursor,
          pageSize: 20,
          orderBy: this.currentSortBy,
          sortDirection: 'desc'
        };

        const response = await commentReplyQueryApi(params);

        if (response.code === 200 && response.data) {
          const { commentReplies, nextCursor, hasNext, actualSize, totalCount } = response.data;

          // 处理评论回复的文件
          const processedReplies = await this.processCommentFiles(commentReplies || []);

          if (isReset) {
            this.paginatedComments = processedReplies;
            // 优先使用 totalCount，如果没有则使用 actualSize
            this.totalCommentCount = totalCount || actualSize || 0;
          } else {
            // 合并数据并去重
            const combinedComments = [...this.paginatedComments, ...processedReplies];
            this.paginatedComments = this.deduplicateComments(combinedComments);
          }

          this.nextCursor = nextCursor;
          this.hasMoreComments = hasNext;
        }
      } catch (error) {
        console.error('查询评论回复失败:', error);
        this.$message?.error('加载评论失败，请稍后重试');
      } finally {
        this.commentLoading = false;
      }
    },

    // 评论数据去重
    deduplicateComments(comments) {
      if (!comments || comments.length === 0) return [];

      const seen = new Set();
      return comments.filter(comment => {
        // 使用 commentReplyId 或 id 作为唯一标识
        const uniqueId = comment.commentReplyId || comment.id;
        if (!uniqueId) {
          return true; // 保留没有ID的评论，但可能导致重复
        }

        if (seen.has(uniqueId)) {
          return false;
        }

        seen.add(uniqueId);
        return true;
      });
    },

    // 处理新评论添加
    async handleNewCommentAdded(newComments) {
      console.log('🚀 handleNewCommentAdded被调用:', {
        commentId: this.item?.commentId,
        newCommentsLength: newComments?.length || 0,
        newCommentIds: newComments?.map(c => c.commentReplyId) || [],
        paginatedCommentsLength: this.paginatedComments.length,
        currentSortBy: this.currentSortBy
      });

      if (!newComments || newComments.length === 0) {
        console.log('❌ 没有新评论，退出处理');
        return;
      }

      console.log('✅ 检测到新评论，开始处理:', newComments.map(c => c.commentReplyId));

      // 如果当前使用分页数据且是按时间排序
      if (this.paginatedComments.length > 0 && this.currentSortBy === 'createdTime') {
        // 按时间排序，将新评论添加到顶部
        console.log('⏰ 按时间排序，添加到顶部');
        const processedNewComments = await this.processCommentFiles(newComments);

        // 去重处理：确保不会重复添加已存在的评论
        const existingIds = new Set(this.paginatedComments.map(c => c.commentReplyId));
        const uniqueNewComments = processedNewComments.filter(c => !existingIds.has(c.commentReplyId));

        if (uniqueNewComments.length > 0) {
          const oldLength = this.paginatedComments.length;
          this.paginatedComments = [...uniqueNewComments, ...this.paginatedComments];
          this.totalCommentCount += uniqueNewComments.length;
          console.log('✨ 评论添加完成:', {
            oldLength,
            newLength: this.paginatedComments.length,
            totalCount: this.totalCommentCount,
            addedCount: uniqueNewComments.length
          });
        } else {
          console.log('⚠️ 所有新评论都已存在，跳过添加');
        }
      } else {
        // 如果是按点赞排序或者没有分页数据，不自动切换排序，只更新总数
        console.log('📊 当前不是按时间排序或没有分页数据，仅更新总数');
        this.totalCommentCount += newComments.length;
        console.log('📈 总数已更新:', this.totalCommentCount);
      }
    },

    // 切换排序方式
    async changeSortBy(sortBy) {
      console.log('🔄 切换排序方式:', {
        currentSortBy: this.currentSortBy,
        targetSortBy: sortBy,
        isSame: this.currentSortBy === sortBy
      });

      // 无论是否相同，都重新查询以获取最新数据
      if (this.currentSortBy === sortBy) {
        console.log('🔄 相同排序方式，重新查询最新数据');
      } else {
        console.log('🔄 不同排序方式，切换并查询');
        this.currentSortBy = sortBy;
      }

      // 清空现有数据，确保重新开始
      this.paginatedComments = [];
      this.nextCursor = null;
      this.hasMoreComments = false;

      // 重新查询数据
      await this.queryCommentReplies(true);

      // 重新绑定滚动监听器（因为DOM可能重新渲染）
      this.bindScrollListener();
    },

    // 加载更多评论
    async loadMoreComments() {
      if (!this.hasMoreComments || this.commentLoading || this.isScrollLoading) {
        return;
      }

      this.isScrollLoading = true;
      try {
        await this.queryCommentReplies(false);
      } catch (error) {
        console.error('加载更多评论失败:', error);
      } finally {
        this.isScrollLoading = false;
      }
    },

    // 处理评论区滚动事件
    handleCommentScroll(e) {
      // 确保事件来源正确
      if (!e || !e.target) {
        return;
      }

      const container = e.target;

      // 验证容器是否为评论区域
      if (!container.classList.contains('comment-block')) {
        return;
      }

      // 清除之前的定时器
      if (this.scrollTimer) {
        clearTimeout(this.scrollTimer);
      }

      // 防抖处理，减少到100ms提高响应速度
      this.scrollTimer = setTimeout(() => {
        try {
          const { scrollTop, scrollHeight, clientHeight } = container;

          // 防止无效数据
          if (scrollHeight <= 0 || clientHeight <= 0) {
            return;
          }

          // 计算距离底部的距离
          const distanceFromBottom = scrollHeight - (scrollTop + clientHeight);

          // 多种触发条件，提高触发概率
          const isNearBottom = distanceFromBottom <= 50; // 增加容差到50px
          const isAtBottom = distanceFromBottom <= 5;    // 严格的底部检测
          const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;
          const isNearBottomByPercentage = scrollPercentage >= 0.9; // 滚动到90%时触发

          // 使用多种条件判断是否应该加载
          const shouldLoad = (isNearBottom || isAtBottom || isNearBottomByPercentage) &&
                           this.hasMoreComments &&
                           !this.commentLoading &&
                           !this.isScrollLoading;

          if (shouldLoad) {
            this.loadMoreComments();
          }
        } catch (error) {
          console.error('滚动检测出错:', error);
        }
      }, 100); // 减少防抖时间到100ms
    },






    // 处理评论回复的文件
    async processCommentFiles(commentReplies) {
      if (!commentReplies || commentReplies.length === 0) {
        return commentReplies;
      }

      // 导入文件处理API
      const { fileGetUrlByIdsApi } = await import("@/api/simulatedVS/index.js");

      return Promise.all(
        commentReplies.map(async (reply) => {
          if (reply.file && reply.file.length > 0) {
            try {
              const res = await fileGetUrlByIdsApi(reply.file);
              // 按文件类型排序（视频在后）
              reply.fileList = res.data.sort((a, b) => {
                const isVideoA = this.videoTypes.includes(a.type?.split('/').pop());
                const isVideoB = this.videoTypes.includes(b.type?.split('/').pop());
                return isVideoA && !isVideoB ? 1 : !isVideoA && isVideoB ? -1 : 0;
              });
            } catch (error) {
              console.error('处理回复文件失败:', error);
              reply.fileList = [];
            }
          }
          return reply;
        })
      );
    },

    // 处理点赞更新事件
    handleLikeUpdate({ commentId, commentReplyId, commentType, likeCount }) {
      const isInAmplifyDialog = this.$el?.closest('.amplify-dialog') !== null;

      console.log('🎯 PostTemplate收到点赞更新事件:', {
        currentCommentId: this.item?.commentId,
        targetCommentId: commentId,
        commentReplyId,
        commentType,
        likeCount,
        componentKey: this.$vnode?.key,
        isInAmplifyDialog
      });

      // 只处理属于当前评论的点赞更新
      if (this.item?.commentId !== commentId) {
        return;
      }

      console.log('✅ 匹配到当前评论，开始更新点赞数据', {
        isInAmplifyDialog,
        componentLocation: isInAmplifyDialog ? '放大弹窗' : '主界面'
      });

      if (commentType == 7) {
        // 回复评论的点赞更新
        this.updateReplyLikeCount(commentReplyId, likeCount);
      } else {
        // 主评论的点赞更新
        this.updateMainCommentLikeCount(likeCount);
      }
    },

    // 更新回复评论的点赞数
    updateReplyLikeCount(commentReplyId, likeCount) {
      console.log('🔄 更新回复评论点赞数:', {
        commentReplyId,
        likeCount,
        paginatedCommentsLength: this.paginatedComments.length,
        drillCommentReplyResLength: this.drillCommentReplyRes?.length || 0,
        paginatedCommentsIds: this.paginatedComments.map(c => c.commentReplyId),
        drillCommentReplyResIds: this.drillCommentReplyRes?.map(c => c.commentReplyId) || []
      });

      let updated = false;

      // 更新分页评论数据
      if (this.paginatedComments.length > 0) {
        const replyIndex = this.paginatedComments.findIndex(
          comment => comment.commentReplyId === commentReplyId
        );
        console.log('🔍 在分页评论中查找:', { replyIndex, found: replyIndex > -1 });
        if (replyIndex > -1) {
          this.$set(this.paginatedComments[replyIndex], 'likeCount', likeCount);
          console.log('✅ 已更新分页评论数据中的点赞数');
          updated = true;
        }
      }

      // 更新旧版本数据（兼容性）
      if (this.drillCommentReplyRes?.length > 0) {
        const replyIndex = this.drillCommentReplyRes.findIndex(
          comment => comment.commentReplyId === commentReplyId
        );
        console.log('🔍 在旧版本评论中查找:', { replyIndex, found: replyIndex > -1 });
        if (replyIndex > -1) {
          this.$set(this.drillCommentReplyRes[replyIndex], 'likeCount', likeCount);
          console.log('✅ 已更新旧版本评论数据中的点赞数');
          updated = true;
        }
      }

      if (!updated) {
        console.log('⚠️ 未在任何数据源中找到目标回复评论:', commentReplyId);
      }
    },

    // 更新主评论的点赞数
    updateMainCommentLikeCount(likeCount) {
      console.log('🔄 更新主评论点赞数:', { likeCount });
      // 主评论的点赞数更新由父组件处理，这里主要是为了完整性
      // 如果需要在PostTemplate中也更新主评论数据，可以在这里添加逻辑
    }
  }
}
</script>

<style scoped lang="scss">
.template-bltw {
  // font-size: 0.8em;
  font-size: 1.1em;
  background-color: #FFFFFF;

  .post-block {
    font-size: 12em;
    display: flex;
    padding: 1em;
    padding-bottom: 0;

    >img {
      width: 3em;
      height: 3em;
      aspect-ratio: 1;
      flex-shrink: 0;
      margin-right: 1em;
      border-radius: 50%;
    }

    .main-content {
      color: #000000;
      width: calc(100% - 6em);

      .user-name {
        // margin-bottom: 0.5em;
        display: flex;
        align-items: center;

        .text {
          font-weight: 600;
          // filter: blur(0.15em);
          margin-right: 1em;
        }

        img {
          width: 1.5em;
          height: 1.5em;
          aspect-ratio: 1;
          flex-shrink: 0;
        }
      }

      .content {
        margin-bottom: 0.5em;
        word-break: break-word;
        white-space: pre-wrap;
      }

      .file-list {
        display: inline-block;

        .img-preview {
          width: 10em;
          height: 10em;
          margin-right: 1em;
        }
      }

      .bottom-icon {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 1em;
        margin-bottom: 1em;

        .icon-item {
          display: flex;
          align-items: center;

          span {
            color: #666666;
          }

          img {
            width: 1.4em;
            margin-right: 0.4em;
            cursor: pointer;
          }
        }
      }
    }
  }

  .comment-block-top {
    width: 100%;
    border-bottom: 0.01em solid #EEEEEE;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0em 12em;

    .top-left {
      color: #333333;
      font-size: 14em;
      font-weight: 600;
      border-bottom: 0.2em solid #EB7350;
      padding: 0.4em;
      margin-bottom: 0.1em;
      width: fit-content;
    }

    .sort-buttons {
      display: flex;
      gap: 5em;

      .sort-btn {
        padding: 0.3em 0.8em;
        border: 0.1em solid #ddd;
        background: #fff;
        color: #666;
        border-radius: 0.3em;
        cursor: pointer;
        font-size: 12em;
        // transition: all 0.3s;

        &:hover {
          border-color: #247CFF;
          color: #247CFF;
        }

        &.active {
          background: #247CFF;
          border-color: #247CFF;
          color: #fff;
        }
      }
    }
  }

  .comment-block {
    max-height: 200em;
    overflow-y: auto;
    overflow-x: hidden;
    /* 确保滚动条可见 */
    scrollbar-width: thin;
    scrollbar-color: #ccc transparent;

    /* Webkit浏览器滚动条样式 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #ccc;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background-color: #999;
    }

    .loading-container {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 1em;
      color: #666;
      font-size: 12em;

      i {
        margin-right: 0.5em;
      }
    }

    .scroll-loading-tip {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 1em;
      color: #666;
      font-size: 12em;

      i {
        margin-right: 0.5em;
        animation: rotate 1s linear infinite;
      }

      @keyframes rotate {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(360deg);
        }
      }
    }

    .no-more-tip {
      text-align: center;
      padding: 1em;
      color: #999;
      font-size: 12em;
    }

    .comment-item {
      font-size: 12em;
      background-color: #FFFFFF;
      display: flex;
      padding: 0.5em 1em;

      >img {
        width: 3em;
        height: 3em;
        aspect-ratio: 1;
        flex-shrink: 0;
        border-radius: 50%;
      }

      .main-content {
        color: #000000;
        padding: 0 1em;
        flex: 1;

        .user-name {
          // color: #646464;
          color: #eb7350;
          // filter: blur(0.15em);
          display: inline-block;
          margin-right: 1em;
        }

        .content {
          margin-bottom: 0.5em;
          word-break: break-word;
          white-space: pre-wrap;
          display: inline;
        }

        .file-list {
          display: inline-block;

          .img-preview {
            width: 10em;
            height: 10em;
            margin-right: 1em;
          }
        }

        .comment-bottom {
          display: flex;
          align-items: end;
          justify-content: space-between;

          .option-icon {
            display: flex;
            align-items: center;

            >img {
              width: 1.3em;
              margin-right: 0.5em;
              cursor: pointer;
            }

            .text {
              color: #999999;
            }
          }
        }
      }

      .team-point {
        width: 1.5em;
        height: 1.5em;
        aspect-ratio: 1;
        flex-shrink: 0;
        margin-right: 1em;
      }
    }
  }

  .createTime {
    font-family: SourceHanSansCN, SourceHanSansCN;
    font-size: 0.9em;
    color: #999999;
  }
}
</style>
