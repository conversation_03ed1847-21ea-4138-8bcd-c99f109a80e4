<template>
  <div ref="treeChart" class="chart" />
</template>
  
<script>
import * as echarts from 'echarts'
export default {
  props: {
    chartData: {
      type: Array,
      default: () => []
    },
    showLoading: {
      type: Boolean,
      default: true
    },
  },
  data() {
    return {
      chart: null,
      timer: null
    }
  },
  watch: {
    chartData() {
      this.$nextTick(() => {
        this.initChart()
      })
    }
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.treeChart)
      // this.chart.showLoading();
      //  let dataArr = [{
      //            name: "工程维修",
      //            value: 125,
      //        },
      //        {
      //            name: "垃圾分类",
      //            value: 15,
      //        }, 
      //        {
      //            name: "垃圾分类",
      //            value: 15,
      //        }, {
      //            name: "环境卫生",
      //            value: 15,
      //        }, {
      //            name: "资产维护",
      //            value: 10,
      //        },
      //        {
      //            name: "长三角联动",
      //            value: 10,
      //        },
      //        {
      //            name: "纠纷调解",
      //            value: 15,
      //        },
      //        {
      //            name: "河道保洁",
      //            value: 51,
      //        },
      //        {
      //            name: "信访调解",
      //            value: 51,
      //        },
      //    ]
      let dataArr = this.chartData
      const data = []
      const colors = ['#FF0000', '#FF7F2D', '#f4b441', '#50b0f9', '#bcd3bb',
                '#1D80DA', '#02F4FF', '#E3BC2D', '#FF6632', '#A7FFB0'];
      for (var i = 0; i < dataArr?.length; i++) {
      const currentColor = colors[i % colors.length];
        data.push(
          {
            name: dataArr[i].areaName,
            value: dataArr[i].issueArticleNum,
            itemStyle: {
                color: {
                    type: 'linear',
                    x: 0,
                    y: 1,
                    x2: 1,
                    y2: 0,
                    colorStops: [{
                        offset: 0,
                        color: '#463428' // 0% 处的颜色
                    }, 
                    {
                        offset: 1,
                        color: currentColor // 100% 处的颜色
                    }],
                    globalCoord: false // 缺省为 false
                }
            },
          }
        )
      }

      const option = {
        // backgroundColor: 'rgba(0, 0, 255, 0.1)',
        calculable: false,
        tooltip: {
            show: true,
            trigger: 'item'
        },
        series: [{
            type: 'treemap',
            left: '0',
            right: '0',
            top: '0',
            bottom: '0',
            label: {
                show: true,
                formatter: (params) => {
                    return params.name
                    // let str = `<div>
                    //             <div style="display: flex">
                    //                 <div style="font-size: 26px">${params.value}</div>
                    //                 <div style="font-size: 18px">%</div>
                    //             </div>
                    //             <div>${params.name}</div>
                    //         </div>`;
                    // return str
                },
                fontSize: '0.14rem',
                // fontWeight: 'bold',
                // ellipsis: true
            },
            breadcrumb: {
                show: false
            },
            
            data: data,
            roam: false,
            nodeClick: false,
        }]
      }
     
      if (!this.showLoading) {
        this.chart.hideLoading()
      }
      this.chart.setOption(option)

    }
  }
}
</script>
<style lang="scss" scoped>
  .chart {
    width: 100%;
    height: 100%;
  }
</style>
  
