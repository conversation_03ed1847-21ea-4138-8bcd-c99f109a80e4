<template>
    <div>
    <div class="screen-major">
        <div class="screen-wrap">
            <div class="screen-title">
                <div class="big-title">
                    <span>宁夏传疫智控云图屏</span>
                </div>
                <div class="screen-time">{{ nowTime }}  &emsp; &nbsp;{{ week }} </div>
            </div>
            <div class="screen-contain">
            <!-- 左 -->
            <div class="screen-chart">
                <div class="deal-situation">
                    <div class="title">
                        <span class="commonTitle">宁夏传染病舆情热搜榜</span>
                    </div>
                    <div class="deal-list">
                        <div v-if="qhHotList.length && !qhHotLoading" class="deal-title">
                            <!-- <span>排名</span>
                            <span style="width:75%">文章名称</span>
                            <span>来源</span> -->
                        </div>
                        <div class="deal-wrap" v-loading="qhHotLoading" element-loading-background="rgba(18, 42, 96,0)"> 
                            <div v-for="(item,index) in qhHotList" :key="index" class="deal-main">
                                <div class="deal-title-pre">
                                    <img v-if="index+1 == 1" src="@/assets/images/champion.png" alt="" class="fire">
                                    <img v-if="index+1 == 2" src="@/assets/images/runner.png" alt="" class="fire">
                                    <img v-if="index+1 == 3" src="@/assets/images/second-runner.png" alt="" class="fire">
                                    <span v-if="index+1>3">{{ index < 9 ? 0 : null}}{{ index + 1 }}</span>
                                </div>
                                <el-tooltip :content="item.title" placement="top" effect="light" :raw-content="true">
                                    <span class="deal-text-left" @click="goOrigin(item)">{{item.title}}</span>
                                </el-tooltip>
                                <span :style=" { textAlign: 'center', padding:'0 0.05rem' }">
                                {{ item.keyword }}
                                </span>
                            </div>  
                            <div v-if="qhHotList.length==0 && !qhHotLoading" class="noneData">
                                <img src="@/assets/images/nodata.png" alt="">
                                <div>正在全力探索数据中...</div>
                            </div>
                        </div>
                    
                    </div>
                </div>
                <div class="deal-situation">
                    <div class="title">
                        <span class="commonTitle">{{areaName}}传染病舆情信息趋势</span>
                    </div>
                    <div class="deal-listwo">
                        <TrendChart 
                        v-loading="qhInfoLoading" 
                        element-loading-background="rgba(18, 42, 96,0)" 
                        style="width:100%;height:100%;" 
                        :chart-data="qhInfoData" />
                    </div>
                </div>
                <!--  -->
                <div class="deal-situation">
                    <div class="title">
                        <span class="commonTitle">{{ areaName }}舆情热词云图</span>
                    </div>
                    <div class="deal-listthree" v-loading="cloudLoading" element-loading-background="rgba(18, 42, 96,0)" >
                        <HotCloud
                            ref="cloud" 
                            style="width:100%;height:100%;overflow:hidden;"   
                            :chartData="cloudData">
                        </HotCloud>
                        <div v-if="cloudData.length==0 && !cloudLoading" class="noneData">
                            <img src="@/assets/images/nodata.png" alt="">
                            <div>正在全力探索数据中...</div>
                        </div>
                    </div>
                </div>

                
                <div class="filter-condition">
                    <el-date-picker
                        popper-class="date-class"
                        :append-body="false"
                        size="mini"
                        v-model="dateValue"
                        :picker-options="pickerOptions"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
                        </el-date-picker>
                        <el-select v-model="queryParams.searchWord" size="mini" placeholder="请选择" class="select-type">
                            <el-option
                            v-for="item in options"
                            :key="item.dictValue"
                            :label="item.dictLabel"
                            :value="item.dictValue">
                            </el-option>
                        </el-select>
                     <el-tooltip placement="top" effect="light">
                        <div slot="content">筛选项对热搜榜以外的纬度生效，热搜榜为最新榜单。</div>
                        <img src="@/assets/images/icon-question.png" alt="" class="name-question">
                    </el-tooltip>
                    <el-button type="primary" style="background:#247cFF;height: 28px;margin-left: 6px;" size="mini" icon="el-icon-search"
                    @click="querySearch"
                    >搜索</el-button>
                </div>
            </div>

            <!-- 中-地图 -->
            <div class="screen-middle">
                <!-- <div class="filter-condition">
                    <el-date-picker
                        popper-class="date-class"
                        :append-body="false"
                        size="mini"
                        v-model="dateValue"
                        :picker-options="pickerOptions"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
                        </el-date-picker>
                        <el-select v-model="queryParams.searchWord" size="mini" placeholder="请选择" class="select-type">
                            <el-option
                            v-for="item in options"
                            :key="item.dictValue"
                            :label="item.dictLabel"
                            :value="item.dictValue">
                            </el-option>
                        </el-select>
                     <el-tooltip placement="top" effect="light">
                        <div slot="content">筛选项对热搜榜以外的纬度生效，热搜榜为最新榜单。</div>
                        <img src="@/assets/images/icon-question.png" alt="" class="name-question">
                    </el-tooltip>
                    <el-button type="primary" style="background:#247cFF;height: 28px;margin-left: 6px;" size="mini" icon="el-icon-search"
                    @click="querySearch"
                    >搜索</el-button>
                </div> -->
                <div class="deal-situation">
                    <div class="title">
                        <span class="commonTitle">全国传染病舆情热搜榜</span>
                    </div>
                    <div class="deal-list">
                        <div v-if="countryHotList.length && !countryHotLoading" class="deal-title">
                            <!-- <span>排名</span>
                            <span style="width:75%">文章名称</span>
                            <span>来源</span> -->
                        </div>
                        <div class="deal-wrap" v-loading="countryHotLoading" element-loading-background="rgba(18, 42, 96,0)"> 
                            <div v-for="(item,index) in countryHotList" :key="index" class="deal-main">
                                <div class="deal-title-pre">
                                    <img v-if="index+1 == 1" src="@/assets/images/champion.png" alt="" class="fire">
                                    <img v-if="index+1 == 2" src="@/assets/images/runner.png" alt="" class="fire">
                                    <img v-if="index+1 == 3" src="@/assets/images/second-runner.png" alt="" class="fire">
                                    <span v-if="index+1>3">{{ index < 9 ? 0 : null}}{{ index + 1 }}</span>
                                </div>
                                <el-tooltip :content="item.title" placement="top" effect="light" :raw-content="true">
                                    <span class="deal-text-left" @click="goOrigin(item)">{{item.title}}</span>
                                </el-tooltip>
                                <span :style=" { textAlign: 'center', padding:'0 0.05rem' }">
                                {{ item.keyword }}
                                </span>
                            </div>  
                            <div v-if="countryHotList.length==0 && !countryHotLoading" class="noneData">
                                <img src="@/assets/images/nodata.png" alt="">
                                <div>正在全力探索数据中...</div>
                            </div>
                        </div>
                    
                    </div>
                </div>
                 <div class="deal-situation">
                    <div class="title">
                        <span class="commonTitle">{{areaName}}传染病舆情分布</span>
                    </div>
                    <!-- style="height: 28vh" -->
                    <div class="deal-listwo" >
                        <DrillMap style="height: 100%;" ref="mapRef" :chartParams="queryParams" @getTreeData="getTreeData"  @getCityCode="getCityCode"></DrillMap>
                    
                    </div>
                </div>
                <div class="deal-situation">
                    <div class="title">
                        <span class="commonTitle">{{areaName}}传染病舆情地区分布</span>
                    </div>
                    <!-- style="height:18vh;" -->
                    <div class="deal-listthree" >
                      <TreeChart 
                        ref="treeRef" 
                        v-if="treeData.length && !treeLoading"
                        style="width:100%;height:100%;"   
                        v-loading="treeLoading" 
                        element-loading-background="rgba(18, 42, 96,0)"
                        :chartData="treeData"
                       ></TreeChart>
                        <div v-if="treeData.length==0 && !treeLoading" class="noneData">
                            <img src="@/assets/images/nodata.png" alt="">
                            <div>正在全力探索数据中...</div>
                        </div>
                    </div>
                   
                </div>
            </div>
            
            <!-- 右 -->
            <div class="screen-chart">
                <div class="deal-situation">
                    <div class="title">
                        <span class="commonTitle">全球传染病舆情分布</span>
                    </div>
                    <div class="deal-list">
                    <!-- <div style="height:25vh"> -->
                        <WorldMap 
                        v-loading="worldMapLoading"
                        :chartData="worldMapData"
                        element-loading-background="rgba(18, 42, 96,0)" 
                        style="width:100%;height:100%;" ></WorldMap>
                    </div>
                </div>
                <div class="deal-situation">
                    <div class="title">
                        <span class="commonTitle">全球传染病舆情信息趋势</span>
                    </div>
                    <div class="deal-listwo">
                          <TrendChart 
                        v-loading="countryInfoLoading" 
                        element-loading-background="rgba(18, 42, 96,0)" 
                        style="width:100%;height:100%;" 
                        :chart-data="countryInfoData" />
                    </div>
                </div>
                <div class="deal-situation">
                    <div class="title">
                        <span class="commonTitle">全球传染病舆情重点信息</span>
                    </div>
                    <div class="deal-listthree">
                        <div style="height:100%;" v-loading="keyInfoLoading" element-loading-background="rgba(18, 42, 96,0)"> 

                            <vue-seamless-scroll 
                                :step="0.1"
                                hover="true"
                                :data="keyInfoList"
                                :class-option="defaultOption(keyInfoList)"
                                class="seamless-warp"
                                >
                                 <div class="deal-wrap">
                                    <div v-for="(item,index) in keyInfoList" :key="index" class="deal-main">
                                        <div class="text-title">
                                            <a target="_blank" :href="item.url">
                                                <span v-html="item.title"></span>
                                            </a>
                                        </div>
                                        <div class="text-name">
                                        
                                            <div class="text-time">
                                                <img src="@/assets/images/time.png" alt="">
                                                发文时间：
                                                {{item.publishTime}}
                                            </div>
                                        </div>
                                    </div>
                                    <div v-if="keyInfoList.length==0 && !keyInfoLoading" class="noneData">
                                        <img src="@/assets/images/nodata.png" alt="">
                                        <div>正在全力探索数据中...</div>
                                    </div>
                                </div>

                            </vue-seamless-scroll>
                        </div>
                        
                    </div>
                </div>
            </div>
            </div>
        </div>
    </div>
    </div>
</template>
<script>
import TreeChart from './TreeChart.vue'
import HotCloud from './HotCloud.vue'
import TrendChart from './TrendChart.vue'
import WorldMap from './WorldMap.vue'
import DrillMap from './DrillMap.vue'
import { getHotwords,getCountryMap,getInfoTrend,getKeyInfo,getHotRanks,getCountryHotInfo,getCountryInfoTrend,getWorldMap } from '@/api/screen/index.js'

export default {
    components: { TrendChart,HotCloud,TreeChart,WorldMap,DrillMap },
    data() {
        return {
          pickerOptions: {
          shortcuts: [{
            text: '最近一周',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }]
        },
            nowTime: '',
            week: '',
            dateValue: [],
            areaName: '宁夏',
            queryParams: {
                startTime: '',
                endTime: '',
                searchWord: '',
                areaCode: '640000', // 默认宁夏 640000
            },
            qhHotList: [],
            qhHotLoading: false,
            countryHotLoading: false,
            countryHotList: [],

            keyInfoList: [],
            keyInfoLoading: false,
            qhInfoLoading: false,
            qhInfoData: {},
            countryInfoLoading: false,
            countryInfoData: {},

            cloudData: [],      
            worldMapLoading: false,
            worldMapData: [],
            options: [],

            treeLoading: false,
            treeData: [],
            areaMapData: {},

     
        }
    },
    computed:{
        defaultOption() {
            return (data) => {
                return {
                step: 0.3, // 数值越大速度滚动越快
                limitMoveNum: 8, // 开始无缝滚动的数据量 this.dataList.length
                hoverStop: true, // 是否开启鼠标悬停stop
                direction: 1, // 0向下 1向上 2向左 3向右
                openWatch: true, // 开启数据实时监控刷新dom
                singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
                singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
                waitTime: 0 // 单步运动停止的时间(默认值1000ms)
                }
            }
        },
    },

    created(){
        this.getweek()
        this.getNowTime()
        this.getDict()
        this.getHotRanks()
        this.getCountryHotInfo()

        this.querySearch()
    },
    methods: {
        goOrigin(item){
            window.open(item.url, '_blank')
        },
        getCityCode(obj){
            this.queryParams.areaCode = obj?.cityCode
            this.areaName = obj?.name
            // this.getAreaData()
            this.getCloudData()
            this.getInfoTrend()
        },
        // 地区数据
        getTreeData(data){
            console.log('data 111:>> ', data);
            this.treeData = data
        },
        async getDict(){
            let res = await this.getDicts('screen_search_word')
            this.options = res.data
            this.options.unshift({dictValue: '', dictLabel: '全部'})
        },
        querySearch(){
            this.getCloudData()
            // this.getAreaData()
            this.getInfoTrend()
            this.getCountryInfoTrend()
            this.getKeyInfomation()
            this.getWorldMap()
            
            const dateArr = this.dateValue
            const params = {
                startTime: dateArr ? dateArr[0] : '',
                endTime: dateArr ? dateArr[1] : '',
                searchWord: this.queryParams.searchWord,
                areaCode: this.queryParams.areaCode
            }
            this.$nextTick(()=>{
                this.$refs.mapRef.nowParams = params
            })
            
        },
        // 青海热搜榜
        async getHotRanks() {
            this.qhHotLoading = true
            const dateArr = this.dateValue
            const params = {
                startTime: dateArr ? dateArr[0] : '',
                endTime: dateArr ? dateArr[1] : '',
                searchWord: this.queryParams.searchWord,
                areaCode: this.queryParams.areaCode
            }
            const res = await getHotRanks(params)
            this.qhHotList = res.data.slice(0,9)
            this.qhHotLoading = false
        },
        // 全国热搜榜
        async getCountryHotInfo() {
            this.countryHotLoading = true
            const dateArr = this.dateValue
            const params = {
                startTime: dateArr ? dateArr[0] : '',
                endTime: dateArr ? dateArr[1] : '',
                searchWord: this.queryParams.searchWord,
            }
            const res = await getCountryHotInfo(params)
            this.countryHotList = res.data.slice(0,9)
            this.countryHotLoading = false
        },
        // 全球重点信息
        async getKeyInfomation(){
            this.keyInfoLoading = true
            const dateArr = this.dateValue
            const params = {
                startTime: dateArr ? dateArr[0] : '',
                endTime: dateArr ? dateArr[1] : '',
                searchWord: this.queryParams.searchWord,
            }
            const res = await getKeyInfo(params)
            this.keyInfoList = res.data
            this.keyInfoLoading = false
        },
        // 全球信息趋势
        async getCountryInfoTrend(){
            this.countryInfoLoading = true
            const dateArr = this.dateValue
            const params = {
                startTime: dateArr ? dateArr[0] : '',
                endTime: dateArr ? dateArr[1] : '',
                searchWord: this.queryParams.searchWord,
            }
            const res = await getCountryInfoTrend(params)
            this.countryInfoData = res.data
            this.countryInfoLoading = false
        },
        // 信息趋势
        async getInfoTrend(){
            this.qhInfoLoading = true
            const dateArr = this.dateValue
            const params = {
                startTime: dateArr ? dateArr[0] : '',
                endTime: dateArr ? dateArr[1] : '',
                searchWord: this.queryParams.searchWord,
                areaCode: this.queryParams.areaCode
            }
            const res = await getInfoTrend(params)
            this.qhInfoData = res.data
            this.qhInfoLoading = false
        },
         // 热词云
        async getCloudData() {
            this.cloudData = []
            this.cloudLoading = true
            const dateArr = this.dateValue
            const params = {
                startTime: dateArr ? dateArr[0] : '',
                endTime: dateArr ? dateArr[1] : '',
                searchWord: this.queryParams.searchWord,
                areaCode: this.queryParams.areaCode
            }
            const res = await getHotwords(params)
            this.cloudLoading = false
            this.cloudData = res.data || []
        },
        // 地区分布
        async getAreaData(){
            this.treeLoading = true
            const dateArr = this.dateValue
            const params = {
                startTime: dateArr ? dateArr[0] : '',
                endTime: dateArr ? dateArr[1] : '',
                searchWord: this.queryParams.searchWord,
                areaCode: this.queryParams.areaCode
            }
            const res = await getCountryMap(params)
            this.treeLoading = false
            this.treeData = res.data || []

           
            // this.$nextTick(()=>{
            //     this.$refs.mapRef.nowParams = params
            // })
        },
        // 世界地图
        async getWorldMap(){
            this.worldMapLoading = true
            const dateArr = this.dateValue
            const params = {
                startTime: dateArr ? dateArr[0] : '',
                endTime: dateArr ? dateArr[1] : '',
                searchWord: this.queryParams.searchWord,
            }
            const res = await getWorldMap(params)
            this.worldMapData = res.data || []
            this.worldMapLoading = false
        },


        // 获取当前时间
        getweek() {
            const now = new Date()
            const day = now.getDay()
            const weeks = [
                '星期日',
                '星期一',
                '星期二',
                '星期三',
                '星期四',
                '星期五',
                '星期六'
            ]
            this.week = weeks[day]
        },
        twoDigits(val) {
            if (val < 10) return '0' + val
            return val
        },
        timeNumber() {
            const today = new Date()
            const date = today.getFullYear() + '/' + this.twoDigits(today.getMonth() + 1) + '/' + this.twoDigits(today.getDate())
            const time = this.twoDigits(today.getHours()) + ':' + this.twoDigits(today.getMinutes()) + ':' + this.twoDigits(today.getSeconds())
            return date + '  ' + time
        },
        getNowTime() {
            const theNowTime = () => {
                this.nowTime = this.timeNumber()
            }
            setInterval(theNowTime, 1000)
        },
    }
}
</script>
<style lang="scss">
.date-class{
  z-index: 9999999 !important;
}
</style>
<style lang="scss" scoped>
@import './index.scss';
</style>

