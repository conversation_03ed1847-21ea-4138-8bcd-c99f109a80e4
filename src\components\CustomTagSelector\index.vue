<template>
  <div class="custom-tag-selector">
    <el-select v-model="selectedTagId" size="mini" placeholder="暂无" filterable :loading="loading"
      @change="handleTagChange" style="width: 100px;">
      <el-option v-for="tag in tagOptions" :key="tag.id" :label="tag.tagContent" :value="tag.id" />
    </el-select>
  </div>
</template>

<script>
import { listTag } from '@/api/system/tag'
import { updateRiskApi } from '@/api/search/index'

export default {
  name: 'CustomTagSelector',
  props: {
    // 当前行数据，必须包含id和md5字段
    rowData: {
      type: Object,
      required: true,
      validator(value) {
        return value && value.id && value.md5
      }
    },
    // 当前选中的标签ID或标签ID数组（兼容多选格式）
    value: {
      type: [Number, String, Array],
      default: null
    }
  },
  data() {
    return {
      loading: false,
      selectedTagId: this.getSelectedTagId(this.value) // 当前选中的标签ID
    }
  },
  computed: {
    // 从全局状态获取标签选项
    tagOptions() {
      return this.$store.getters['tagOptions/tagOptions'] || []
    }
  },
  watch: {
    value(newVal) {
      this.selectedTagId = this.getSelectedTagId(newVal)
    }
  },
  created() {
    this.ensureTagOptionsLoaded()
  },
  methods: {
    // 从value中获取选中的标签ID（兼容多种格式）
    getSelectedTagId(value) {
      if (value === null || value === undefined) {
        return null
      }
      if (Array.isArray(value)) {
        if (value.length === 0) {
          return null
        }
        const firstItem = value[0]
        // 如果数组元素是对象格式 {id: "xxx", tagContent: "xxx"}
        if (typeof firstItem === 'object' && firstItem.id) {
          return firstItem.id
        }
        // 如果数组元素是简单的ID值
        return firstItem
      }
      // 如果是单个值，直接返回
      return value
    },

    // 检测原始数据是否为对象数组格式
    isObjectArrayFormat(value) {
      return Array.isArray(value) &&
        value.length > 0 &&
        typeof value[0] === 'object' &&
        value[0].id &&
        value[0].tagContent
    },

    // 推断数组应该使用的格式（用于空数组的情况）
    inferArrayFormat() {
      // 如果原始值有内容，直接检测
      if (this.isObjectArrayFormat(this.value)) {
        return 'object'
      }
      // 如果是空数组，根据字段名推断（contentMeta通常是对象数组）
      // 这里可以根据实际业务需求调整推断逻辑
      return 'object' // 默认使用对象格式，因为新的contentMeta格式是对象数组
    },

    // 将单个标签ID转换为兼容的格式
    formatTagValue(tagId) {
      if (tagId === null || tagId === undefined) {
        return null
      }

      // 根据原始value的格式决定返回格式
      if (Array.isArray(this.value)) {
        if (!tagId) {
          return []
        }

        // 检查原始数组的格式
        const arrayFormat = this.value.length > 0 ?
          (this.isObjectArrayFormat(this.value) ? 'object' : 'simple') :
          this.inferArrayFormat()

        if (arrayFormat === 'object') {
          // 返回对象格式
          const selectedTag = this.tagOptions.find(tag => tag.id === tagId)
          if (selectedTag) {
            return [{
              id: selectedTag.id,
              tagContent: selectedTag.tagContent
            }]
          }
          return []
        } else {
          // 返回简单ID数组格式
          return [tagId]
        }
      }

      // 如果原始值是单个值格式，返回单个值
      return tagId
    },

    // 确保标签选项已加载（使用全局状态管理）
    async ensureTagOptionsLoaded() {
      // 如果全局状态中已有标签数据，直接返回
      if (this.$store.getters['tagOptions/tagOptions'] && this.$store.getters['tagOptions/tagOptions'].length > 0) {
        return
      }

      // 如果正在加载中，等待加载完成
      if (this.$store.getters['tagOptions/tagOptionsLoading']) {
        return
      }

      // 触发全局加载
      try {
        await this.$store.dispatch('tagOptions/loadTagOptions')
      } catch (error) {
        console.error('加载标签选项失败:', error)
        this.$message.error('加载标签选项失败')
      }
    },

    // 处理标签变更
    async handleTagChange(tagId) {
      try {
        // 格式化要发送给API的值（API期望的格式）
        const apiValue = tagId || null

        const param = {
          md5: this.rowData.md5,
          indexId: this.rowData.id,
          changeType: 2, // 自定义标签变更类型
          changeValue: apiValue
        }

        const response = await updateRiskApi(param)

        if (response.code === 200) {
          this.$message.success('标签更新成功')

          // 格式化要返回给父组件的值（保持与原始格式一致）
          const formattedValue = this.formatTagValue(tagId)

          // 触发v-model更新
          this.$emit('input', formattedValue)
          // 触发change事件
          this.$emit('change', formattedValue)
          // 更新本地选中值
          this.selectedTagId = tagId
        } else {
          this.$message.error(response.msg || '标签更新失败')
          // 恢复原值
          this.selectedTagId = this.getSelectedTagId(this.value)
        }
      } catch (error) {
        console.error('更新标签失败:', error)
        this.$message.error('更新标签失败')
        // 恢复原值
        this.selectedTagId = this.getSelectedTagId(this.value)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-tag-selector {
  display: inline-block;
  margin: 2px 4px;

  ::v-deep {
    .el-input.el-input--mini.el-input--suffix {
      // width: 48px;
    }

    .el-input .el-select__caret.is-reverse {
      margin-top: -4px;
    }

    .el-icon-arrow-up:before {
      content: '\e78f';
    }

    .el-input--suffix {
      .el-input__inner {
        text-overflow: ellipsis;
        padding-right: 5px;
        background-color: #E8F4FF;
        color: #409EFF;
      }

    }

    .el-input .el-select__caret {
      color: #409EFF;
    }
  }
}
</style>