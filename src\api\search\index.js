import request from '@/utils/request'

// 查询定时任务调度列表
export function hotList(data) {
  return request({
    url: '/hot/rank/list',
    method: 'post',
    data
  })
}

// 查询浙江政法热点
export function hotZJList(data) {
  return request({
    url: '/home/<USER>',
    method: 'post',
    data
  })
}

// 获取平台热榜数据
export function hotRankList(params) {
  return request({
    url: '/hot/rankList',
    method: 'get',
    params
  })
}

// 新增热词
export function hotWordAdd(data) {
  return request({
    url: '/hot/word/add',
    method: 'post',
    data
  })
}

// 查询热词
export function hotWordList(data) {
  return request({
    url: '/hot/word/list',
    method: 'post',
    data
  })
}

// 删除热词
export function hotWordDelete(data) {
  return request({
    url: '/hot/word/delete',
    method: 'delete',
    data
  })
}

// 推荐词
export function recommendWordListApi(data) {
  return request({
    url: '/recommend/word/list',
    method: 'post',
    data
  })
}

// 搜索舆情数据
export function searchData(data) {
  return request({
    url: '/search/search',
    method: 'post',
    data
  })
}

// 搜索舆情数据方案
export function searchPlan(data) {
  return request({
    url: '/search/plan',
    method: 'post',
    data
  })
}

// 获取地域树
export function areaTree(areaId, deep) {
  return request({
    url: `/area/tree/${areaId}/${deep}`,
    method: 'get'
  })
}

// 搜索相似数
export function similarCount(data) {
  return request({
    url: `/search/similarCount`,
    method: 'post',
    data
  })
}

// 敏感信息占比
export function emotionAnalyse(data) {
  return request({
    url: `/analyse/emotionAnalyse`,
    method: 'post',
    data
  })
}

// 敏感信息top10
export function emotionAnalyseTop(data) {
  return request({
    url: `/analyse/emotionAnalyseTop`,
    method: 'post',
    data
  })
}

// 信息来源占比
export function mediaTypeAnalyse(data) {
  return request({
    url: `/analyse/mediaTypeAnalyse`,
    method: 'post',
    data
  })
}

// 关键词云
export function wordAnalyse(data) {
  return request({
    url: `/analyse/wordAnalyse`,
    method: 'post',
    data
  })
}

// 修改情感
export function updateEmotion(data) {
  return request({
    url: `/search/updateEmotion`,
    method: 'post',
    data
  })
}

// 修改噪音标记
export function updateTrash(data) {
  return request({
    url: `/search/updateTrash`,
    method: 'post',
    data
  })
}

// 修改处置标记
export function updateDeal(data) {
  return request({
    url: `/search/updateDeal`,
    method: 'post',
    data
  })
}

// 修改重点关注标记
export function updateFollow(data) {
  return request({
    url: `/search/updateFollow`,
    method: 'post',
    data
  })
}

// 已读文章
export function searchRead(data) {
  return request({
    url: `/search/read`,
    method: 'post',
    data
  })
}

// 实时数据刷新
export function realTimeInfoCount(data) {
  return request({
    url: `/search/realTimeInfoCount`,
    method: 'post',
    data
  })
}

// 搜索导出
export function searchExport(data) {
  return request({
    url: `/search/export`,
    method: 'post',
    data
  })
}


// 地图数据
export function getAreaData(dict) {
  return request({
    url: '/issue/screen/areaData/' + dict,
    method: 'get'
  })
}

// 详情页-详情内容
export function infoOne(data) {
  return request({
    url: `/info/one`,
    method: 'post',
    data
  })
}

// 详情页-相似信息
export function infoSimilar(data) {
  return request({
    url: `/info/similar`,
    method: 'post',
    headers: {
      isToken: false
    },
    data
  })
}

// 详情页-词云
export function infoWord(data) {
  return request({
    url: `/info/keyWord`,
    method: 'post',
    data
  })
}

// 媒体活跃统计
export function getMediaActiveMap(data) {
  return request({
    url: '/analyse/mediaActiveMap',
    method: 'post',
    data
  })
}

// 地域分布统计
export function getAreaMap(data) {
  return request({
    url: '/analyse/areaMap',
    method: 'post',
    data
  })
}

// 敏感走势图
export function getEmtion(data) {
  return request({
    url: '/analyse/time/emotion',
    method: 'post',
    data
  })
}

// 信息来源走势图
export function getType(data) {
  return request({
    url: '/analyse/time/type',
    method: 'post',
    data
  })
}

// 保存舆情监测条件
export function saveSearchCriteria(data) {
  return request({
    url: '/search/saveSearchCriteria',
    method: 'post',
    data
  })
}

// 获取舆情监测条件
export function getSearchCriteria(data) {
  return request({
    url: '/search/getSearchCriteria',
    method: 'post',
    data
  })
}

// 媒体数量
export function getMediaCentral(data) {
  return request({
    url: '/analyse/media/central',
    method: 'post',
    data
  })
}

// 媒体分布图
export function getMedia(data) {
  return request({
    url: '/analyse/media/level',
    method: 'post',
    data
  })
}

// 搜索列表-返回是否删除
export function getUrlAccessStatusApi(data) {
  return request({
    url: '/search/getUrlAccessStatus',
    method: 'post',
    data
  })
}

// 新增联系人
export function addContactsApi(data) {
  return request({
    url: '/message/contacts',
    method: 'post',
    data
  })
}

// 修改联系人
export function updataContactsApi(data) {
  return request({
    url: '/message/contacts',
    method: 'put',
    data
  })
}

// 删除联系人
export function delContactsApi(ids) {
  return request({
    url: '/message/contacts/' + ids,
    method: 'delete'
  })
}

//获取联系人
export function getContactsApi(query) {
  return request({
    url: '/message/contacts/all',
    method: 'get',
    params: query
  })
}

// 发送短信
export function sendMsgApi(data) {
  return request({
    url: '/search/result/sendMsg',
    method: 'post',
    data
  })
}

// 增加过滤信息
export function insertFilterInfoApi(data) {
  return request({
    url: '/info/insertFilterInfo',
    method: 'post',
    data
  })
}

// 获取媒体各类型数量
export function getMediaTypeCount(data) {
  return request({
    url: '/search/mediaTypeCount',
    method: 'post',
    data
  })
}

// 账号类型统计
export function accountLevelCount(data) {
  return request({
    url: '/search/accountLevelCount',
    method: 'post',
    data
  })
}

// 相似文章详情页
export function similarityApi(data) {
  return request({
    url: '/search/similarity',
    method: 'post',
    data
  })
}

// 相似文章导出
export function similarityExportApi(data) {
  return request({
    url: '/similarity/export',
    method: 'post',
    data
  })
}

// 摘要
export function infoSummary(data) {
  return request({
    url: '/info/getSummary',
    method: 'post',
    data
  })
}

// 热门话题、相关热搜
export function hotsearchApi(data) {
  return request({
    url: '/analyse/hotsearch',
    method: 'post',
    data
  })
}

// 事件摘要
export function analyseSummaryApi(data) {
  return request({
    url: '/analyse/summary',
    method: 'post',
    data
  })
}

// 媒体观点
export function mediaOpinionApi(data) {
  return request({
    url: '/analyse/mediaOpinion',
    method: 'post',
    data
  })
}

// 网民观点
export function netizenOpinionApi(data) {
  return request({
    url: '/analyse/netizenOpinion',
    method: 'post',
    data
  })
}

// 专项监测详情->方案分词
export function detailWordApi(data) {
  return request({
    url: '/info/detail',
    method: 'post',
    data
  })
}

// 批量已读
export function readManyApi(data) {
  return request({
    url: '/search/readMany',
    method: 'post',
    data
  })
}

// 统计分析-传播路径
export function propagePathApi(data) {
  return request({
    url: '/analyse/propagate',
    method: 'post',
    data
  })
}
// 作者详情
export function accountDetailApi(data) {
  return request({
    url: '/api/local/account',
    method: 'post',
    data
  })
}

// 作者详情-文章列表
export function accountArticleListApi(data) {
  return request({
    url: '/api/local/article',
    method: 'post',
    data
  })
}

// 详情-实体识别
export function entityIdentificationApi(data) {
  return request({
    url: '/entityIdentification/entityOption',
    method: 'post',
    data
  })
}

// 更新风险等级
export function updateRiskApi(data) {
  return request({
    url: '/search/updateChange',
    method: 'post',
    data
  })
}

// 新增搜索记录
export function esBeanMarkSaveApi(data) {
  return request({
    url: '/esBeanMark/save',
    method: 'post',
    data
  })
}
// 搜索记录查询
export function esBeanMarkListApi(data) {
  return request({
    url: '/esBeanMark/list',
    method: 'post',
    data
  })
}
// 删除记录查询
export function esBeanMarkDeleteApi(data) {
  return request({
    url: '/esBeanMark/delete',
    method: 'post',
    data
  })
}

// 导出搜索记录
export function esBeanMarkExportApi(data) {
  return request({
    url: '/esBeanMark/export',
    method: 'post',
    data
  })
}

// 收藏搜索记录
export function esBeanMarkAddMaterialApi(data) {
  return request({
    url: '/esBeanMark/material/add',
    method: 'post',
    data
  })
}