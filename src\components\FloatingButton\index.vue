<template>
  <!--  子组件 -->
  <div class="float_button">
    <div v-hasPermi="['public:iframe:screen']" @click="handleIframeScreen" class="float_screen" style="right: 160px;">
      <span class="text">
        <img src="@/assets/images/data.png" alt="">
      </span>
    </div>
    <div v-hasPermi="['public:screen:nx']" @click="handleNxScreen" class="float_screen" style="right: 135px;">
      <span class="text">
        <img src="@/assets/images/data.png" alt="">
      </span>
    </div>
    <div v-hasPermi="['public:screen:import']" @click="handleScreen" class="float_screen" style="right: 135px;">
      <span class="text">
        <img src="@/assets/images/data.png" alt="">
      </span>
    </div>

    <div @click="onBtnClicked" ref="floatButton" class="float_info">
      <!-- :style="{'width': itemWidth + 'px','margin':'5px 10px', 'height': itemHeight + 'px', 'left': left + 'px', 'top': top + 'px'}" -->
      <span class="text" @click="openNotice">
        <img src="@/assets/images/hasNotice.png" alt="" v-if="unReadCount > 0">
        <img src="@/assets/images/notice.png" alt="" v-else>
      </span>
    </div>
    <!-- 弹窗 -->
    <el-dialog title="通知中心" width="25%" top="10vh" class="dialog__wrapper notice_wrapper" @close="handleClose"
      :visible.sync="drawer">
      <span slot="title">
        <img class="tip-img" src="@/assets/images/bee.png" alt="">
        <span class="notice-text"> 通知中心 <i style="cursor:pointer" class="el-icon-setting" @click="msgTips"></i></span>
      </span>
      <div class="contain" v-show="pageLists.length>0">
        <div class="tip-box" v-for="(item,index) in pageLists" :key="index">
          <div class="tip-type">
            <div v-show="item.noticeType"
              :class="item.noticeType === '1'? 'mark' : item.noticeType === '2'? 'mark2' : item.noticeType === '3' ? 'mark3' : 'mark4'"
              class="common">
              {{item.noticeType == '1' ? '重点关注' : item.noticeType == '2' ? '短信报送' : item.noticeType == '3' ? '已处置' :
              '预警'}}
            </div>
          </div>
          <div class="tip-left">
            <div class="notice-title" @click="goDetail(item)">
              {{ index + 1 }}. <span v-html="item.title"></span> </div>
            <div><span class="fit">{{item.noticeTime}}</span> <span class="fit">来源：{{ item.typeName }}</span></div>
          </div>
          <div class="tip-right" @click="goDetail(item)"><img src="@/assets/images/right-arrow.png" alt=""></div>
        </div>
        <!-- <div style="text-align:center;"> <el-button v-show="noMore" :disabled="!disabled" :loading="timelineLoading" type="text"
           @click="moreTimeline">加载更多</el-button></div> -->
      </div>
      <el-empty v-show="total==0" v-loading="timelineLoading" description="无数据"></el-empty>

    </el-dialog>

    <el-dialog class="dialog__wrapper tip_wrapper" title="通知设置" width="20%" :visible.sync="msgSetVisible">
      <div>
        <el-radio v-model="enableNotify" :label="true">消息提示</el-radio>
      </div>
      <div style="height:26px;"></div>
      <div>
        <el-radio v-model="enableNotify" :label="false">消息不提示</el-radio>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handlerSetting">确 定</el-button>
        <el-button @click="msgSetVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {getNoticeLists, getUserSetting, setUserSetting, getUserNotice} from '@/api/float.js'
import {transImage} from '@/utils/index';

export default {

  data() {
    return {
      clientWidth: 0,
      clientHeight: 0,
      timer: null,
      currentTop: 0,
      left: 90,
      top: 20,

      itemWidth: 70,  //按钮宽度
      isShort: true,
      itemHeight: 32, // 悬浮按钮高度
      gapWidth: 0,    // 距离左右两边距离
      coefficientHeight: 0.8,  // 从上到下距离比例

      drawer: false,
      msgSetVisible: false,
      loading: false,
      pageLists: [],
      total: 0,
      pageParams: {
        pageNum: 1,
        pageSize: 3,
      },
      transImage,
      enableNotify: true,
      unReadCount: undefined,
      timelineLoading: false,
      intervalId: null,
    }
  },
  computed: {
    noMore() {
      return this.total > this.pageLists.length
    },
    disabled() {
      return this.timelineLoading || this.noMore
    }
  },
  // watch: {
  //   drawer: {
  //     handler(val) {
  //         if(!this.drawer){
  //           this.intervalId = setInterval(this.getSetting, 50000); // 10000毫秒 = 10秒
  //         }
  //     },
  //   },
  // },
  created() {

    // console.log('屏幕宽度', document.documentElement.clientWidth)
    // console.log('屏幕高度度', document.documentElement.clientHeight)
    let type = navigator.userAgent;

    // console.log('设备', type)

    this.clientWidth = document.documentElement.clientWidth
    this.clientHeight = document.documentElement.clientHeight
    // this.left = this.clientWidth - this.itemWidth - this.gapWidth - 20;
    // this.top = this.clientHeight * this.coefficientHeight
    // this.left = this.clientWidth - this.itemWidth - this.gapWidth - 100;
    // this.top = 20

    // if(this.drawer == false){
    this.intervalId = setInterval(this.getSetting, 5000); // 10000毫秒 = 10秒
    // }
  },
  beforeDestroy() {
    // 添加监听页面滚动
    window.removeEventListener('scroll', this.handleScrollStart)
    // 组件销毁前清除定时器，防止内存泄漏
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  },
  methods: {
    handleScreen() {
      const fullPath = this.$router.resolve({ path: '/homeScreen/start'})
      window.open(fullPath.href, '_blank')
    },
    // 宁夏疾控入口
    handleNxScreen() {
      const fullPath = this.$router.resolve({ path: '/homeScreen/nxScreen' })
      window.open(fullPath.href, '_blank')
    },
    // 安庆大屏入口
    handleIframeScreen() {
      const fullPath = this.$router.resolve({ path: '/iframe/screen'})
      window.open(fullPath.href, '_blank')
    },
    init() {
      this.pageParams.pageNum = 1
      this.pageLists = []
      this.total = 0
      this.getLists()
    },
    // 跳转详情页
    async goDetail(row) {
      console.log('row :>> ', row);
      let query = { id: row.id, planId: row.planId, keyWords: row.hitWords, time: row.time, md5: row.md5 }
      if (row.noticeType == '4') {
        query.keyWords = row.hitWord.join(' ')
      }
      const fullPath = this.$router.resolve({ path: '/fullSearch/dataDetail', query })
      window.open(fullPath.href, '_blank')
    },
    async getSetting() {
      const res = await getUserSetting()
      const rep = await getUserNotice()
      this.unReadCount = rep.data.unReadCount
      this.enableNotify = res.data.enableNotify

      // 提示
      if (this.enableNotify && this.unReadCount > 0) {
        this.drawer = true
        if (this.intervalId) {
          clearInterval(this.intervalId);
          this.intervalId = null;
          this.init()
        }
      } else {
        this.drawer = false
      }
    },
    handlerSetting() {
      setUserSetting({enableNotify: this.enableNotify}).then(res => {
        if (res.code == 200) {
          this.$message.success('设置成功')
          this.msgSetVisible = false
          this.drawer = false
        }
      }).catch(error => {
        this.msgSetVisible = false
        this.drawer = false
        console.log('error :>> ', error);
      })
    },
    getLists() {
      let params = {
        pageSize: 3,
        pageNum: this.pageParams.pageNum,
      }
      this.loading = true
      this.timelineLoading = true

      getNoticeLists(params).then(res => {
        this.loading = false
        this.timelineLoading = false
        if (res.code == 200) {
          this.total = res.total
          this.pageLists = [...this.pageLists, ...res.rows]
        }
      }).catch(error => {
        this.timelineLoading = false
        console.log('error :>> ', error);
      })
    },
    openNotice() {
      this.drawer = true
      if (this.intervalId) {
        clearInterval(this.intervalId);
        this.intervalId = null;
        this.init()
      }
    },
    handleClose() {
      this.intervalId = setInterval(this.getSetting, 5000);
    },
    msgTips() {
      this.msgSetVisible = true
    },
    moreTimeline() {
      this.pageParams.pageNum = this.pageParams.pageNum + 1
      this.getLists()
    },

    onBtnClicked() {

    },
    handleScrollStart() {
      console.log('这是啥时候触发呀？ScrollStart')
      this.timer && clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.handleScrollEnd()
      }, 300)
      this.currentTop = document.documentElement.scrollTop || document.body.scrollTop
      if (this.left > this.clientWidth / 2) {
        this.left = this.clientWidth - this.itemWidth / 2
      } else {
        this.left = -this.itemWidth / 2
      }
    },
    handleScrollEnd() {
      let scrollTop = document.documentElement.scrollTop || document.body.scrollTop
      if (scrollTop === this.currentTop) {
        if (this.left > this.clientWidth / 2) {
          this.left = this.clientWidth - this.itemWidth - this.gapWidth
        } else {
          this.left = this.gapWidth
        }
        clearTimeout(this.timer)
      }
    },

  },
  mounted() {
    this.$nextTick(() => {


      const floatButton = this.$refs.floatButton
      floatButton.addEventListener("touchstart", () => {
        floatButton.style.transition = 'none'
      })

      // 在拖拽的过程中，组件应该跟随手指的移动而移动。
      floatButton.addEventListener("touchmove", (e) => {
        // console.log('移动中', e)
        if (e.targetTouches.length === 1) {         // 一根手指
          document.body.addEventListener('touchmove', this.bodyScroll, {passive: false});  //禁止页面滑动
          let touch = e.targetTouches[0]
          this.left = touch.clientX - 20
          this.top = touch.clientY - 25
        }

      })

      // 拖拽结束以后，重新调整组件的位置并重新设置过度动画。
      floatButton.addEventListener("touchend", () => {
        floatButton.style.transition = 'all 0.3s'
        console.log('拖拽结束后left', this.left)
        document.body.removeEventListener('touchmove', this.bodyScroll, {passive: false});  //解除页面禁止滑动
        if (this.left > document.documentElement.clientWidth / 2) {
          this.left = document.documentElement.clientWidth - this.itemWidth - 20;

        } else {
          this.left = 0
        }
      })
    })
  },
}
</script>
<style lang="scss" scoped>
.tableItemImg {
  width: 29px;
  vertical-align: text-top;
  margin-right: 10px;
}

.tip-img {
  position: relative;
  left: -70px;
  top: -54px;
  width: 103px;
  height: 103px;
}

.notice-text {
  position: relative;
  top: -88px;
  left: -70px;
}

.float_button {
  display: flex;
  justify-content: flex-start;
  align-items: center;

  .tip_wrapper {
    ::v-deep .el-dialog {
      margin-right: 50%;
      margin-top: 30vh !important;
    }

    ::v-deep .el-dialog__footer {
      text-align: center;
    }
  }

  .notice_wrapper {
    ::v-deep .el-empty {
      padding: 0px;
    }

    ::v-deep .el-empty__image {
      display: none;
    }

    .contain {
      overflow-y: auto;
      max-height: 600px;
      border-radius: 5px;
    }

    /* 隐藏 WebKit 浏览器的滚动条 */
    .contain::-webkit-scrollbar {
      display: none;
    }

    ::v-deep .el-dialog {
      margin-right: 90px;
      margin-top: 55vh !important;
    }

    ::v-deep .el-dialog__body {
      padding: 0px;
      min-height: 100px;
    }
  }

  .dialog__wrapper {
    ::v-deep .el-dialog {
      border-radius: 5px;

      .el-dialog__header {
        height: 60px;
        background: #F4F7FB;
        color: #000000;
        font-size: 16px;
        border-radius: 5px;
      }
    }


    .tip-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border: 1px solid #EFEFEF;
      padding: 20px 0px;
      padding-left: 15px;

      .tip-type {
        width: 17%;
        display: flex;
        justify-content: center;
      }

      .common {
        border-radius: 2px;
        font-size: 12px;
        line-height: 20px;
        height: 20px;
        position: relative;
        padding: 0px 6px;
        top: -12px;
        width: fit-content;
        text-align: center;
      }

      .mark {
        background: #F9D0AC;
        color: #F87500;
      }

      .mark2 {
        background: #A2F0AC;
        color: #0A6A17;
      }

      .mark3 {
        color: #00B4D8;
        background: #B2E8F3;
      }

      .mark4 {
        background: #FDED9A;
        color: #9C840C;
      }

      .tip-left {
        width: 75%;

        // margin-left: 40px;
        .notice-title {
          font-weight: bold;
          font-size: 18px;
          color: #333333;
          line-height: 25px;    text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
              margin-bottom: 6px;
              cursor: pointer;
          }
          .fit{
            font-size: 14px;
            color: #999999;
            line-height: 20px;
            margin-left: 16px;
          
          }
        }
        .tip-right{
          width: 6%;
          // margin-right: 10px;
          cursor: pointer;
          img{
            width: 11px;
            height: 20px;
          }
        }
  
      }
    }
  
    .float_screen{
      margin-left: 10px;
      box-shadow: #1666ca;
      // position: fixed;
      // top: 30px;
      // right: 150px;
      z-index: 9 !important;
      cursor: pointer;
      .text {
        font-size: 12px;
        color: #fff;
        display: flex;
        align-items: center;
        img {
          width: 28px;
          height: 24px;
        }
      }
    }
    .float_info {
      margin-left: 10px;
      box-shadow: #1666ca;
      // transition: 0;
      // position: fixed;
      // position: fixed;
      // top: 30px;
      // right: 110px;
      // margin: 5px 10px;
      // display: flex;
      // flex-flow: row;
      // justify-content: center;
      // align-items: center;
      z-index: 9 !important;
      // background: #1666ca;
      // background-color: rgba(22, 102, 202, 0.6);
      // border-radius: 10px;
      cursor: pointer;
      .text {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #fff;
        img {
          width: 20px;
          height: 24px;
        }
      }
    }
  }

</style>
