.screen-major {
    width: 100%;
    height: 100%;
    overflow: hidden;

    .screen-wrap {
        width: 100%;
        height: 100vh;
        overflow: hidden;
        background: url("../../../assets/screen/background.jpg") no-repeat center center;
        background-size: 100% 100%;

        .screen-title {
            position: relative;
            width: 100%;
            height: 10vh;
            line-height: 9vh;
            color: #fff;
            font-size: 0.34rem;
            margin: 2vh 0 0 0;
            text-align: center;
            background: url("../../../assets/screen/title.png") no-repeat center center;
            background-size: 100% 100%;
            overflow: hidden;

            .big-title {
                text-shadow: 4px 0px 2px #051920;
                font-size: .48rem;
                font-weight: 700;
                position: absolute;
                top: 50%;
                left: 50%;
                -webkit-transform: translate(-50%, -80%);
                transform: translate(-50%, -80%);
                font-family: PangMenZhengDao;
            }
        }
    }

    .screen-contain {
        display: flex;
        justify-content: space-between;
        padding: 0 3% 3% 2%;

        // 左右
        .screen-chart {
            width: 32%;


            .deal-list {
                background: url("../../../assets/screen/quadrangle.png") no-repeat center center;
                background-size: 100% 100%;
                margin-left: 5%;
                padding: 1.5%;
                // padding-top: 4%;
                height: 22vh;
                overflow: hidden;
                box-sizing: border-box;

                .deal-title {
                    font-size: 0.16rem;
                    display: flex;
                    justify-content: center;
                    text-align: center;
                    color: #02F4FF;
                    font-weight: 500;
                }

                /* 隐藏 WebKit 浏览器的滚动条 */
                .deal-wrap::-webkit-scrollbar {
                    display: none;
                }

                .deal-wrap {
                    height: 22vh;
                    overflow-y: auto;

                    .deal-title-pre {
                        width: 15%;
                        display: flex;
                        justify-content: center;

                        img {
                            width: 0.17rem;
                            height: 0.22rem;
                        }
                    }

                    .deal-main {
                        display: flex;
                        justify-content: flex-start;
                        font-size: 0.16rem;
                        line-height: 0.22rem;
                        color: #fff;
                        padding: 0.1rem 0;
                        align-items: center;
                        text-align: center;

                        span.deal-text-left {
                            text-align: left;
                        }

                        span:nth-child(1) {
                            color: #FFFFFF;
                            font-size: 0.14rem;
                            display: inline-block;
                            width: 0.2rem;
                            height: 0.2rem;
                            border-radius: 50%;
                            background: #00B3FF;
                            line-height: 0.2rem;
                        }

                        span:nth-child(2) {
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            width: 66%;
                            cursor: pointer;
                            // margin: 0 0.2rem;
                            color: #FFFFFF;
                        }

                        span:nth-child(3) {
                            width: 19%;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            // flex: 1;
                            color: #FFFFFF;

                            .fire {
                                width: 0.17rem;
                                height: 0.21rem;
                                margin-bottom: -5px;
                                margin-right: 2px;
                            }
                        }
                    }
                }
            }

            // 
            .deal-listwo {
                background: url("../../../assets/screen/quadrangle.png") no-repeat center center;
                background-size: 100% 100%;
                margin-left: 5%;
                padding: 1%;
                // padding-top: 4%;
                box-sizing: border-box;
                height: 24vh;

                .badText {
                    color: #fff;
                    font-size: 0.14rem;
                    padding-left: 20%;
                    padding-top: 0.15rem;
                }
            }

            .deal-listthree {
                background: url("../../../assets/screen/quadrangle.png") no-repeat center center;
                background-size: 100% 100%;
                margin-left: 5%;
                padding: 1.5%;
                // padding-top: 4%;
                height: 26vh;
                box-sizing: border-box;

                .deal-wrap {
                    // height: 22vh;
                    height: 100%;
                    overflow: hidden;

                    .deal-main {
                        // display: flex;
                        // justify-content: center;
                        font-size: 0.14rem;
                        // color: #fff;
                        padding: 2% 2%;
                        // align-items: center;

                        width: 100%;
                        // height: 100%;
                        // overflow: hidden;
                        border-bottom: dotted 1px #2f333f;
                        // height: 70px;
                        // padding: 6px 0px;
                        box-sizing: border-box;
                        cursor: pointer;

                        .text-title {
                            width: 100%;
                            overflow: hidden;
                            height: 22px;
                            line-height: 22px;
                            color: #4dadef;
                            text-overflow: ellipsis;
                            white-space: nowrap;

                            a {
                                color: #4dadef;
                                font-size: 0.16rem;
                            }
                        }

                        .text-name {
                            width: 100%;
                            overflow: hidden;
                            display: flex;
                            flex-direction: row;
                            justify-content: space-between;
                            height: 0.22rem;
                            line-height: 0.22rem;
                            font-size: 0.14rem;

                            .source {
                                color: #4dadef;
                            }

                            .text-time {
                                color: #fff;
                            }

                            img {
                                width: 0.14rem;
                                height: 0.14rem;
                                display: inline-block;
                                vertical-align: text-bottom;
                                margin-right: 2px;
                            }
                        }
                    }
                }


            }
        }

        // 中
        .screen-middle {
            // width: 48%;
            width: 32%;
            // flex: 1;
            // margin: 0 3%;
            font-size: 0.24rem;
            color: #fff;
            justify-content: space-around;



        }
    }
}

.filter-condition {
    margin-left: 5%;
    display: flex;
    // padding: 2%;
    padding-bottom: 0;
    height: 4vh;
    position: absolute;
    top: 8vh;
    width: 28vw;
    margin-left: 2vw;

    .el-range-editor.el-input__inner {
        background-color: transparent;
        // height: 0.28rem;
        // line-height: 0.28rem;
    }

    ::v-deep .el-range-input {
        background-color: transparent;
        color: #fff;
    }

    ::v-deep .el-range-separator {
        color: #fff;
    }

    .select-type {
        width: 30%;
        margin: 0px 10px;
        margin-right: 4px;

        ::v-deep .el-input--mini .el-input__inner {
            background: none;
            color: #fff;
            // height: 0.28rem;
            // line-height: 0.28rem;
        }
    }

    img {
        width: 0.18rem;
        height: 0.18rem;
        margin-top: 5px;

        &.name-question {
            width: 0.16rem;
        }
    }

}


// 公共头
.deal-situation:nth-child(2) {
    margin: 3% 0;
}

.deal-situation {
    margin-right: 1%;
    box-sizing: border-box;

    .title {
        padding-left: 15%;
        padding-bottom: 3%;
        margin-bottom: -4px;
        font-size: 0.2rem;
        color: #fff;
        font-weight: bold;
        // font-style: italic;

        background: url("../../../assets/screen/charts-title.png") no-repeat center center;
        background-size: 100% 100%;

        .commonTitle {
            display: inline-block;
            transform: skew(-20deg, 0deg);
            letter-spacing: 2px;
            background: linear-gradient(to bottom, #95ddff 30%, #EFFCFE 70%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
    }

    .deal-list {
        background: url("../../../assets/screen/quadrangle.png") no-repeat center center;
        background-size: 100% 100%;
        margin-left: 5%;
        padding: 1.5%;
        // padding-top: 4%;
        height: 22vh;
        overflow: hidden;
        box-sizing: border-box;

        .deal-title {
            font-size: 0.16rem;
            display: flex;
            justify-content: center;
            text-align: center;
            color: #02F4FF;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .deal-wrap::-webkit-scrollbar {
            display: none;
        }

        .deal-wrap {
            height: 22vh;
            overflow-y: auto;

            .deal-title-pre {
                width: 15%;
                display: flex;
                justify-content: center;

                img {
                    width: 0.17rem;
                    height: 0.22rem;
                }
            }

            .deal-main {
                display: flex;
                justify-content: flex-start;
                font-size: 0.16rem;
                line-height: 0.22rem;
                color: #fff;
                padding: 0.1rem 0;
                align-items: center;
                text-align: center;

                span.deal-text-left {
                    text-align: left;
                }

                span:nth-child(1) {
                    color: #FFFFFF;
                    font-size: 0.14rem;
                    display: inline-block;
                    width: 0.2rem;
                    height: 0.2rem;
                    border-radius: 50%;
                    background: #00B3FF;
                    line-height: 0.2rem;
                }

                span:nth-child(2) {
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    width: 66%;
                    cursor: pointer;
                    // margin: 0 0.2rem;
                    color: #FFFFFF;
                }

                span:nth-child(3) {
                    width: 19%;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    // flex: 1;
                    color: #FFFFFF;

                    .fire {
                        width: 0.17rem;
                        height: 0.21rem;
                        margin-bottom: -5px;
                        margin-right: 2px;
                    }
                }
            }
        }
    }

    // 
    .deal-listwo {
        background: url("../../../assets/screen/quadrangle.png") no-repeat center center;
        background-size: 100% 100%;
        margin-left: 5%;
        padding: 1%;
        // padding-top: 4%;
        box-sizing: border-box;
        height: 24vh;

        .badText {
            color: #fff;
            font-size: 0.14rem;
            padding-left: 20%;
            padding-top: 0.15rem;
        }
    }

    .deal-listthree {
        background: url("../../../assets/screen/quadrangle.png") no-repeat center center;
        background-size: 100% 100%;
        margin-left: 5%;
        padding: 1.5%;
        // padding-top: 4%;
        height: 26vh;
        box-sizing: border-box;

        .deal-wrap {
            // height: 22vh;
            height: 100%;
            overflow: hidden;

            .deal-main {
                // display: flex;
                // justify-content: center;
                font-size: 0.14rem;
                // color: #fff;
                padding: 2% 2%;
                // align-items: center;

                width: 100%;
                // height: 100%;
                // overflow: hidden;
                border-bottom: dotted 1px #2f333f;
                // height: 70px;
                // padding: 6px 0px;
                box-sizing: border-box;
                cursor: pointer;

                .text-title {
                    width: 100%;
                    overflow: hidden;
                    height: 22px;
                    line-height: 22px;
                    color: #4dadef;
                    text-overflow: ellipsis;
                    white-space: nowrap;

                    a {
                        color: #4dadef;
                        font-size: 0.16rem;
                    }
                }

                .text-name {
                    width: 100%;
                    overflow: hidden;
                    display: flex;
                    flex-direction: row;
                    justify-content: space-between;
                    height: 0.22rem;
                    line-height: 0.22rem;
                    font-size: 0.14rem;

                    .source {
                        color: #4dadef;
                    }

                    .text-time {
                        color: #fff;
                    }

                    img {
                        width: 0.12rem;
                        height: 0.12rem;
                        display: inline-block;
                        vertical-align: text-bottom;
                        margin-right: 2px;
                    }
                }
            }
        }


    }
}

.screen-time {
    position: absolute;
    font-size: 0.16rem;
    right: 2%;
    top: -3vh;
}

.infoTab {
    display: flex;
    justify-content: flex-end;
    color: #fff;
    font-size: 0.12rem;
    margin-bottom: 1.2%;

    span {
        width: 14%;
        height: 2.4vh;
        line-height: 2.4vh;
        text-align: center;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #FFFFFF;
        margin-right: 2%;
        opacity: 0.8;
        cursor: pointer;

        &.active {
            color: #3BE0E8;
            opacity: 1;
            border: 1px solid #3BE0E8;
        }
    }
}

.noneData {
    color: #fff;
    font-size: 0.14rem;
    text-align: center;
    margin-top: 0.2rem;

    img {
        width: 1.2rem;
        height: 1.24rem;
    }
}

.deal-operate {
    color: #fff;
    font-size: 0.16rem;

    .operate-title {
        padding-left: 2%;
        opacity: 0.8;
    }

    .operate-select {
        width: 2rem;
    }
}

.seamless-warp {
    overflow: hidden;
    color: #fff;
    padding: 1%;
    height: 100%;

    .item {
        height: 100%;

        .title {
            padding: 6px;
            display: inline-block;
        }
    }
}