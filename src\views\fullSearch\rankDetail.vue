<template>
  <div class="rank-detail-wrap">
    <div class="rank-head">
      <img class="head-img" src="@/assets/images/search-bg.png" alt="">
    </div>
    <div class="rank-rank">
      <div class="sider-nav">
        <div class="nav-list">
          <!-- <div :class="{'navbar': true, 'active': isActive == 0 }" @click="changeType(0,0,4)"><img
            src="@/assets/images/judicial-img.png" alt=""> 全国政法热点
          </div>
          <div :class="{'navbar': true, 'active': isActive == 11 }" @click="changeType(11,0,5)"><img
            src="@/assets/images/judicial-img.png" alt=""> 浙江政法热点
          </div>
          <div :class="{'navbar': true, 'active': isActive == 1 }" @click="changeType(1,0,1)"><img
            src="@/assets/images/judicial-img.png" alt=""> 法院
          </div>
          <div :class="{'navbar': true, 'active': isActive == 2 }" @click="changeType(2,0,2)"><img
            src="@/assets/images/judicial-img.png" alt=""> 检察
          </div>
          <div :class="{'navbar': true, 'active': isActive == 3 }" @click="changeType(3,0,3)"><img
            src="@/assets/images/judicial-img.png" alt=""> 刑事事件
          </div> -->
          <div :class="{'navbar': true, 'active': isActive == 4 }" @click="changeType(4,1,1,0)"><img
            src="@/assets/images/3.png" alt=""> 微博实时热搜排行
          </div>
          <div :class="{'navbar': true, 'active': isActive == 5 }" @click="changeType(5,1,1,1)"><img
            src="@/assets/images/3.png" alt=""> 微博实时要闻榜
          </div>
          <div :class="{'navbar': true, 'active': isActive == 6 }" @click="changeType(6,1,2,0)"><img class="square"
                                                                                                     src="@/assets/images/douyin.png"
                                                                                                     alt=""> 抖音实时热点排行
          </div>
          <div :class="{'navbar': true, 'active': isActive == 7 }" @click="changeType(7,1,2,1)"><img class="square"
                                                                                                     src="@/assets/images/douyin.png"
                                                                                                     alt=""> 抖音实时视频榜
          </div>
          <div :class="{'navbar': true, 'active': isActive == 8 }" @click="changeType(8,1,3,0)"><img class="square"
                                                                                                     src="@/assets/images/baidu.png"
                                                                                                     alt=""> 百度实时舆情排行
          </div>
          <div :class="{'navbar': true, 'active': isActive == 9 }" @click="changeType(9,1,3,1)"><img class="square"
                                                                                                     src="@/assets/images/baidu.png"
                                                                                                     alt=""> 百度今日舆情排行
          </div>
          <div :class="{'navbar': true, 'active': isActive == 10 }" @click="changeType(10,1,3,2)"><img class="square"
                                                                                                       src="@/assets/images/baidu.png"
                                                                                                       alt=""> 百度一周舆情排行
          </div>
        </div>
      </div>

      <div class="hot-style" v-loading="baiduLoading">

        <div class="single-rank">
          <div class="single-title">
            <img src="@/assets/images/judicial-img.png" alt="" v-if="rankType==0">
            <img src="@/assets/images/3.png" alt="" v-if="rankType==1">
            <img src="@/assets/images/douyin.png" class="square" alt="" v-if="rankType==2">
            <img src="@/assets/images/baidu.png" class="square" alt="" v-if="rankType==3">
            <div class="title-box">
              <p>{{ rankTitle }}</p>
              <span>榜单时间：{{rankTime}} 实时更新</span>
            </div>
          </div>
          <div v-loading="politicsLoading" class="rank-box-wrap">
            <div class="rank-list" v-for="(item,index) in rankList" :key="index">
              <img src="@/assets/images/hot.png" alt="" v-if="index==0">
              <img src="@/assets/images/warm.png" alt="" v-else-if="index==1">
              <img src="@/assets/images/cold.png" alt="" v-else-if="index==2">
              <span class="rank-noimg" v-else></span>
              <span :class="index==0?'num num-first':index==1?'num num-second':index==2?'num num-third':'num'">{{index + 1}}</span>
              <div class="detail rank-article" @click="viewOrigin(item.url)">{{item.title}}</div>
              <div>{{ item.indexNum }}</div>
            </div>
          </div>
        </div>
      </div>

    </div>
    <!-- <FloatingButton/> -->
  </div>
</template>
<script>
import {hotList, hotRankList, hotZJList} from "@/api/search/index";

export default {
  data() {
    return {
      rankTitle: '',
      rankTime: '',
      rankList: [],
      rankType: null,
      politicsLaw: [],
      politicsZJLaw: [],
      politicsLoading: false,
      courtData: [],
      courtLoading: false,
      prosecutorsData: [],
      prosecutorsLoading: false,
      caseData: [],
      caseLoading: false,
      wordsData: [],
      wordsLoading: false,
      isActive: 0,
      realTimeData: [],
      newsData: [],
      weiboLoading: false,
      hotData: [],
      videoData: [],
      douyinLoading: false,
      opinionRealTimeData: [],
      dayData: [],
      weekData: [],
      baiduLoading: false,
    }
  },
  async created() {
    this.baiduLoading = true
    await this.queryPolitics()
    // await this.queryZJPolitics()
    // await this.queryCourt()
    // await this.queryProsecutors()
    // await this.queryCase()
    await this.platWeiboList()
    await this.platDouyinList()
    await this.platBaiduList()
    if (this.$route.query.type == 0) {
      this.changeType(0, 0, 4)
    }
    if (this.$route.query.type == 1) {
      this.changeType(1, 0, 1)
    }
    if (this.$route.query.type == 2) {
      this.changeType(2, 0, 2)
    }
    if (this.$route.query.type == 3) {
      this.changeType(3, 0, 3)
    }
    if (this.$route.query.type == 4) {
      this.changeType(4, 1, 1, 0)
    }
    if (this.$route.query.type == 5) {
      this.changeType(5, 1, 1, 1)
    }
    if (this.$route.query.type == 6) {
      this.changeType(6, 1, 2, 0)
    }
    if (this.$route.query.type == 7) {
      this.changeType(7, 1, 2, 1)
    }
    if (this.$route.query.type == 8) {
      this.changeType(8, 1, 3, 0)
    }
    if (this.$route.query.type == 9) {
      this.changeType(9, 1, 3, 1)
    }
    if (this.$route.query.type == 10) {
      this.changeType(10, 1, 3, 2)
    }
    if (this.$route.query.type == 11) {//浙江政法热点
      this.changeType(11, 0, 5)
    }

  },
  mounted() {
    let container = document.getElementsByClassName('topbar-container')
    if (container.length > 0) {
      let links = container[0].querySelectorAll('a');
      let searchPage = ''
      // 遍历这些a标签
      links.forEach((link) => {
        // 检查href属性是否包含"info"
        if (link.href.includes('searchRank')) {
          let liEle = link.querySelector('li')
          searchPage = liEle
        }
      });
      if (this.$route.path == '/fullSearch/rankDetail') {
        setTimeout(() => {
          searchPage.classList.add('is-active');
        }, 0);
      }
    }
  },
  methods: {
    // 0:changeType(0,0,4) 1:changeType(1,0,1) 2:changeType(2,0,2) 3:changeType(3,0,3)
    // 4:changeType(4,1,1,0) 5:changeType(5,1,1,1) 6:changeType(6,1,2,0) 7:changeType(7,1,2,1)
    // 8:changeType(8,1,2,0) 9:changeType(9,1,2,1) 10:changeType(10,1,2,2)
    changeType(index, type, number, sort) {
      this.isActive = index
      if (type == 0) {
        this.rankType = 0
        // 非平台
        if (number == 4) {
          // 政法
          this.rankList = this.politicsLaw
          this.rankTime = this.rankList[0].updateTime
          this.rankTitle = '全国政法热点'
        } else if (number == 1) {
          // 法院
          this.rankList = this.courtData
          this.rankTime = this.rankList[0].updateTime
          this.rankTitle = '法院'
        } else if (number == 2) {
          // 检察
          this.rankList = this.prosecutorsData
          this.rankTime = this.rankList[0].updateTime
          this.rankTitle = '检察'
        } else if (number == 3) {
          // 刑事事件
          this.rankList = this.caseData
          this.rankTime = this.rankList[0].updateTime
          this.rankTitle = '刑事事件'
        } else if (number == 5) {
          // 浙江政法热点
          this.rankList = this.politicsZJLaw
          this.rankTime = this.rankList[0]?.updateTime
          this.rankTitle = '浙江政法热点'
        }
      } else {
        // 平台
        if (number == 1) {
          this.rankType = 1
          // 微博
          if (sort == 0) {
            this.rankList = this.realTimeData
            this.rankTime = this.rankList[0].updateTime
            this.rankTitle = '微博实时热搜排行'
          } else {
            this.rankList = this.newsData
            this.rankTime = this.rankList[0].updateTime
            this.rankTitle = '微博实时要闻榜'
          }
        } else if (number == 2) {
          this.rankType = 2
          // 抖音
          if (sort == 0) {
            this.rankList = this.hotData
            this.rankTime = this.rankList[0].updateTime
            this.rankTitle = '抖音实时热点排行'
          } else {
            this.rankList = this.videoData
            this.rankTime = this.rankList[0].updateTime
            this.rankTitle = '抖音实时视频榜'
          }
        } else if (number == 3) {
          this.rankType = 3
          // 百度
          if (sort == 0) {
            this.rankList = this.opinionRealTimeData
            this.rankTime = this.rankList[0].updateTime
            this.rankTitle = '百度实时舆情排行'
          } else if (sort == 1) {
            this.rankList = this.dayData
            this.rankTime = this.rankList[0].updateTime
            this.rankTitle = '百度今日舆情排行'
          } else {
            this.rankList = this.weekData
            this.rankTime = this.rankList[0].updateTime
            this.rankTitle = '百度一周舆情排行'
          }
        }
      }
    },
    // 查看原文
    viewOrigin(url) {
      window.open(url, '_blank')
    },
    // 获取全国政法热点排行
    async queryPolitics() {
      try {
        this.politicsLoading = true
        let res = await hotList({type: 4, count: 50})
        this.politicsLaw = res.data
      } finally {
        // this.politicsLoading = false
      }
    },
    // 获取浙江政法热点排行
    async queryZJPolitics() {
      try {
        this.politicsLoading = true
        let res = await hotZJList({timeType: this.$route.query.timeType, count: 50})
        this.politicsZJLaw = res.data
      } finally {
        // this.politicsLoading = false
      }
    },
    // 获取法院排行
    async queryCourt() {
      try {
        this.courtLoading = true
        let res = await hotList({type: 1, count: 50})
        this.courtData = res.data
      } finally {
        this.courtLoading = false
      }
    },
    // 获取检察排行
    async queryProsecutors() {
      try {
        this.prosecutorsLoading = true
        let res = await hotList({type: 2, count: 50})
        this.prosecutorsData = res.data
      } finally {
        this.prosecutorsLoading = false
      }
    },
    // 获取刑事事件排行
    async queryCase() {
      try {
        this.caseLoading = true
        let res = await hotList({type: 3, count: 50})
        this.caseData = res.data
      } finally {
        this.caseLoading = false
      }
    },
    // 平台- 微博热榜数据
    async platWeiboList() {
      try {
        this.weiboLoading = true
        let res = await hotRankList({type: 1, count: 50})
        this.realTimeData = res.data.realTime
        this.newsData = res.data.news
      } finally {
        this.weiboLoading = false
      }
    },
    // 平台- 抖音热榜
    async platDouyinList() {
      try {
        this.douyinLoading = true
        let res = await hotRankList({type: 2, count: 50})
        this.hotData = res.data.hot
        this.videoData = res.data.video
      } finally {
        this.douyinLoading = false
      }
    },
    // 平台- 百度热榜
    async platBaiduList() {
      try {
        this.baiduLoading = true
        let res = await hotRankList({type: 3, count: 50})
        this.opinionRealTimeData = res.data.realTime
        this.dayData = res.data.day
        this.weekData = res.data.week
      } finally {
        this.baiduLoading = false
        this.politicsLoading = false
      }
    }
  }
}
</script>
<style scoped lang="scss">
@import "./rank.scss";
</style>
