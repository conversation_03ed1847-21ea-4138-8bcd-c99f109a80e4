<template>
  <div class="search-wrap">
    <div class="search-head">
      <img class="head-img" src="@/assets/images/search-bg.png" alt="">
      <div class="head-wrap">
        <div class="head-title">智慧舆情全文搜索</div>
        <div class="head-input">
          <el-input placeholder="请输入内容" ref="myInput" v-model="keywords" @focus="showSlide" @input="focusSlide"
                    @mouseup.stop.native="focusSlide" @keyup.enter.native="handleSearch" class="input-with-select">
            <template slot="prepend">
              <div class="append-head">关键词<span class="line"></span></div>
            </template>
            <el-button slot="append" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          </el-input>
          <transition name="el-zoom-in-top">
            <div ref="addInput" v-show="slideFlag" class="slide-box">
              <span class="slide-item" v-for="item in slideList" @mousedown="addSlide(item)">{{item}}</span>
            </div>
          </transition>
          <el-popover placement="top-start" width="500" trigger="hover">
            <div>
              1：“+”表示“并且”,“|”表示“或” <br/>2：什么情况下用“|”：如想关注北京或上海或广州的新闻，表达式为“北京|上海|广州”，表示文章中出现
              “北京”、“上海”、“广州”任意一个城市就能监测到。
              <br/>3：什么情况下用“+”：如想关注北京车牌摇号的新闻，表达式为“北京+车牌摇号”，表示文章中同时出现
              “北京”和“车牌摇号”两个关键词才能监测到。
              <br/> 4：什么情况下同时用到“+”、“|”：如想关注上海世博会的新闻，由于“世博会”又可能被称为“世界博览会”，表达式为
              “上海+(世博会|世界博览会)”，表示文章中出现“上海”，同时出现“世博会”或者 “世界博览会”中任意一个词，就能监测到；
            </div>
            <img slot="reference" src="@/assets/images/icon-question-white.png" alt="" class="name-question">
          </el-popover>
          <span class="search-log-btn" @click="goSearchLog()">搜索记录</span>
        </div>
        <div class="hot-words" v-if="wordsData.length>0">
          <span class="hot-title">搜索热词</span>
          <div class="word-list" v-loading="wordsLoading">
            <div class="words-main" v-for="(item,index) in wordsData" :key="index">
              <img v-if="index==0" src="@/assets/images/hot.png" alt="">
              <img v-if="index==1" src="@/assets/images/warm.png" alt="">
              <img v-if="index==2" src="@/assets/images/cold.png" alt="">
              <span class="keywords-content" @click="setKeywords(item)">{{item}}</span>
              <i class="el-icon-error" @click="delKeywords(item)"></i>
            </div>
          </div>
        </div>
        <div class="hot-words" v-if="recommendWordsData.length>0">
          <span class="hot-title">相关推荐词</span>
          <div class="word-list" v-loading="recommendWordsLoading">
            <div class="words-main" v-for="(item,index) in recommendWordsData" :key="index">
              <span class="keywords-content" @click="setKeywords(item)">{{item}}</span>
              <!-- <i class="el-icon-error" @click="delKeywords(item)"></i> -->
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="search-rank">
      <!-- <div class="sider-nav">
        <div class="nav-list">
          <div :class="{'navbar': true, 'active': isActive == 0 }" @click="changeType(0)"><img
            src="@/assets/images/judicial-img.png" alt=""> 司法
          </div>
          <div :class="{'navbar': true, 'active': isActive == 1 }" @click="changeType(1)"><img
            src="@/assets/images/plat-img.png" alt=""> 平台
          </div>
        </div>
      </div> -->

      <div v-show="isActive== 0" class="hot-style">

        <div class="single-rank">
          <div class="single-title">
            <span>政法热点</span>
            <span class="single-more" @click="handleMore(0)">更多>></span>
          </div>
          <div v-loading="politicsLoading" class="rank-wrap">
            <div class="rank-list" v-for="(item,index) in politicsLaw" :key="index">
              <img src="@/assets/images/hot.png" alt="" v-if="index==0">
              <img src="@/assets/images/warm.png" alt="" v-else-if="index==1">
              <img src="@/assets/images/cold.png" alt="" v-else-if="index==2">
              <span class="rank-noimg" v-else></span>
              <span :class="index==0?'num num-first':index==1?'num num-second':index==2?'num num-third':'num'">{{index + 1}}</span>
              <div class="detail rank-article" @click="viewOrigin(item.url)">{{item.title}}</div>
              <div>{{ item.indexNum }}</div>
            </div>
          </div>
        </div>
        <div class="single-rank">
          <div class="single-title">
            <span>法院</span>
            <span class="single-more" @click="handleMore(1)">更多>></span>
          </div>
          <div v-loading="courtLoading" class="rank-wrap">
            <div class="rank-list" v-for="(item,index) in courtData" :key="index">
              <img src="@/assets/images/hot.png" alt="" v-if="index==0">
              <img src="@/assets/images/warm.png" alt="" v-else-if="index==1">
              <img src="@/assets/images/cold.png" alt="" v-else-if="index==2">
              <span class="rank-noimg" v-else></span>
              <span :class="index==0?'num num-first':index==1?'num num-second':index==2?'num num-third':'num'">{{index + 1}}</span>
              <span class="detail rank-article" @click="viewOrigin(item.url)">{{item.title}}</span>
              <div>{{ item.indexNum }}</div>
            </div>
          </div>
        </div>
        <div class="multy-rank">
          <div class="multy-wrap">
            <div class="single-title">
              <span>检察</span>
              <span class="single-more" @click="handleMore(2)">更多>></span>
            </div>
            <div v-loading="prosecutorsLoading" class="rank-wrap">
              <div class="rank-list" v-for="(item,index) in prosecutorsData" :key="index">
                <img src="@/assets/images/hot.png" alt="" v-if="index==0">
                <img src="@/assets/images/warm.png" alt="" v-else-if="index==1">
                <img src="@/assets/images/cold.png" alt="" v-else-if="index==2">
                <span class="rank-noimg" v-else></span>
                <span :class="index==0?'num num-first':index==1?'num num-second':index==2?'num num-third':'num'">{{index + 1}}</span>
                <span class="detail rank-article" @click="viewOrigin(item.url)">{{item.title}}</span>
                <div>{{ item.indexNum }}</div>
              </div>
            </div>
          </div>
          <div class="multy-wrap">
            <div class="single-title">
              <span>刑事事件</span>
              <span class="single-more" @click="handleMore(3)">更多>></span>
            </div>
            <div v-loading="caseLoading" class="rank-wrap">
              <div class="rank-list" v-for="(item,index) in caseData" :key="index">
                <img src="@/assets/images/hot.png" alt="" v-if="index==0">
                <img src="@/assets/images/warm.png" alt="" v-else-if="index==1">
                <img src="@/assets/images/cold.png" alt="" v-else-if="index==2">
                <span class="rank-noimg" v-else></span>
                <span :class="index==0?'num num-first':index==1?'num num-second':index==2?'num num-third':'num'">{{index + 1}}</span>
                <span class="detail rank-article" @click="viewOrigin(item.url)">{{item.title}}</span>
                <div>{{ item.indexNum }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-show="isActive == 1" class="plat-style">
        <div class="nav-title">微博热榜</div>
        <div class="nav-box" v-loading="weiboLoading">
          <div class="single-rank rank-two">
            <div class="single-title">
              <span>实时热搜排行</span>
              <span class="single-more" @click="handleMore(4)">更多>></span>
            </div>
            <div class="rank-wrap">
              <div class="rank-list" v-for="(item,index) in realTimeData" :key="index">
                <img src="@/assets/images/hot.png" alt="" v-if="index==0">
                <img src="@/assets/images/warm.png" alt="" v-else-if="index==1">
                <img src="@/assets/images/cold.png" alt="" v-else-if="index==2">
                <span class="rank-noimg" v-else></span>
                <span :class="index==0?'num num-first':index==1?'num num-second':index==2?'num num-third':'num'">{{index + 1}}</span>
                <div class="detail rank-article" @click="viewOrigin(item.url)">{{item.title}}</div>
                <div>{{ item.indexNum }}</div>
              </div>
            </div>
          </div>

          <div class="single-rank rank-two margin-two">
            <div class="single-title">
              <span>实时要闻榜</span>
              <span class="single-more" @click="handleMore(5)">更多>></span>
            </div>
            <div class="rank-wrap">
              <div class="rank-list" v-for="(item,index) in newsData" :key="index">
                <img src="@/assets/images/hot.png" alt="" v-if="index==0">
                <img src="@/assets/images/warm.png" alt="" v-else-if="index==1">
                <img src="@/assets/images/cold.png" alt="" v-else-if="index==2">
                <span class="rank-noimg" v-else></span>
                <span :class="index==0?'num num-first':index==1?'num num-second':index==2?'num num-third':'num'">{{index + 1}}</span>
                <span class="detail rank-article" @click="viewOrigin(item.url)">{{item.title}}</span>
                <div>{{ item.indexNum }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="nav-title comm-style">抖音热榜</div>
        <div class="nav-box" v-loading="douyinLoading">
          <div class="single-rank rank-two">
            <div class="single-title">
              <span>实时热点排行</span>
              <span class="single-more" @click="handleMore(6)">更多>></span>
            </div>
            <div class="rank-wrap">
              <div class="rank-list" v-for="(item,index) in hotData" :key="index">
                <img src="@/assets/images/hot.png" alt="" v-if="index==0">
                <img src="@/assets/images/warm.png" alt="" v-else-if="index==1">
                <img src="@/assets/images/cold.png" alt="" v-else-if="index==2">
                <span class="rank-noimg" v-else></span>
                <span :class="index==0?'num num-first':index==1?'num num-second':index==2?'num num-third':'num'">{{index + 1}}</span>
                <div class="detail rank-article" @click="viewOrigin(item.url)">{{item.title}}</div>
                <div>{{ item.indexNum }}</div>
              </div>
            </div>
          </div>

          <div class="single-rank rank-two margin-two">
            <div class="single-title">
              <span>实时视频榜</span>
              <span class="single-more" @click="handleMore(7)">更多>></span>
            </div>
            <div class="rank-wrap">
              <div class="rank-list" v-for="(item,index) in videoData" :key="index">
                <img src="@/assets/images/hot.png" alt="" v-if="index==0">
                <img src="@/assets/images/warm.png" alt="" v-else-if="index==1">
                <img src="@/assets/images/cold.png" alt="" v-else-if="index==2">
                <span class="rank-noimg" v-else></span>
                <span :class="index==0?'num num-first':index==1?'num num-second':index==2?'num num-third':'num'">{{index + 1}}</span>
                <span class="detail rank-article" @click="viewOrigin(item.url)">{{item.title}}</span>
                <div>{{ item.indexNum }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="nav-title  comm-style">百度舆情热榜</div>
        <div class="nav-box" v-loading="baiduLoading">
          <div class="single-rank rank-three">
            <div class="single-title">
              <span>实时舆情排行</span>
              <span class="single-more" @click="handleMore(8)">更多>></span>
            </div>
            <div class="rank-wrap">
              <div class="rank-list" v-for="(item,index) in opinionRealTimeData" :key="index">
                <img src="@/assets/images/hot.png" alt="" v-if="index==0">
                <img src="@/assets/images/warm.png" alt="" v-else-if="index==1">
                <img src="@/assets/images/cold.png" alt="" v-else-if="index==2">
                <span class="rank-noimg" v-else></span>
                <span :class="index==0?'num num-first':index==1?'num num-second':index==2?'num num-third':'num'">{{index + 1}}</span>
                <div class="detail rank-article" @click="viewOrigin(item.url)">{{item.title}}</div>
                <div>{{ item.indexNum }}</div>
              </div>
            </div>
          </div>

          <div class="single-rank rank-three">
            <div class="single-title">
              <span>今日舆情排行</span>
              <span class="single-more" @click="handleMore(9)">更多>></span>
            </div>
            <div class="rank-wrap">
              <div class="rank-list" v-for="(item,index) in dayData" :key="index">
                <img src="@/assets/images/hot.png" alt="" v-if="index==0">
                <img src="@/assets/images/warm.png" alt="" v-else-if="index==1">
                <img src="@/assets/images/cold.png" alt="" v-else-if="index==2">
                <span class="rank-noimg" v-else></span>
                <span :class="index==0?'num num-first':index==1?'num num-second':index==2?'num num-third':'num'">{{index + 1}}</span>
                <span class="detail rank-article" @click="viewOrigin(item.url)">{{item.title}}</span>
                <div>{{ item.indexNum }}</div>
              </div>
            </div>
          </div>
          <div class="single-rank rank-three margin-two">
            <div class="single-title">
              <span>一周舆情排行</span>
              <span class="single-more" @click="handleMore(10)">更多>></span>
            </div>
            <div class="rank-wrap">
              <div class="rank-list" v-for="(item,index) in weekData" :key="index">
                <img src="@/assets/images/hot.png" alt="" v-if="index==0">
                <img src="@/assets/images/warm.png" alt="" v-else-if="index==1">
                <img src="@/assets/images/cold.png" alt="" v-else-if="index==2">
                <span class="rank-noimg" v-else></span>
                <span :class="index==0?'num num-first':index==1?'num num-second':index==2?'num num-third':'num'">{{index + 1}}</span>
                <span class="detail rank-article" @click="viewOrigin(item.url)">{{item.title}}</span>
                <div>{{ item.indexNum }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>
<script>
import {hotList, hotRankList, hotWordAdd, hotWordList, hotWordDelete, recommendWordListApi} from "@/api/search/index";

export default {
  data() {
    return {
      slideFlag: false,
      slideList: ['+', '|', '(', ')'],
      keywords: '',
      politicsLaw: [],
      politicsLoading: false,
      courtData: [],
      courtLoading: false,
      prosecutorsData: [],
      prosecutorsLoading: false,
      caseData: [],
      caseLoading: false,
      wordsData: [],
      wordsLoading: false,
      recommendWordsLoading: false,
      recommendWordsData: [],
      isActive: 1,
      realTimeData: [],
      newsData: [],
      weiboLoading: false,
      hotData: [],
      videoData: [],
      douyinLoading: false,
      opinionRealTimeData: [],
      dayData: [],
      weekData: [],
      baiduLoading: false,
    }
  },
  created() {
    this.queryWordList()
    this.queryRecommendWordList()
    this.queryPolitics()
    this.queryCourt()
    this.queryProsecutors()
    this.queryCase()

    this.platWeiboList()
    this.platDouyinList()
    this.platBaiduList()
  },
  mounted() {
    document.addEventListener("mouseup", (e) => {
      let treeDom = this.$refs.addInput
      if (treeDom) {
        if (!treeDom.contains(e.target)) {
          this.slideFlag = false;
        }
      }
    });
    if (this.$route.query?.flag == 'plat') {
      this.isActive = 1
    } else {
      this.isActive = 1
    }
  },
  beforeDestroy() {
    document.removeEventListener('mouseup', this.showSlide);
  },
  methods: {
    // 更多热榜数据
    handleMore(item) {
      const fullPath = this.$router.resolve({path: '/fullSearch/rankDetail', query: {type: item}})
      window.open(fullPath.href, '_blank')
    },
    changeType(val) {

      this.isActive = val
    },
    // 插入文字
    insertTextAtCursor(text) {
      const input = this.$refs.myInput.$el.querySelector('input');
      if (document.activeElement === input) {
        // 获取光标位置
        const startPos = input.selectionStart;
        const endPos = input.selectionEnd;
        // 插入文字
        const newValue =
          input.value.substring(0, startPos) +
          text +
          input.value.substring(endPos, input.value.length);
        // 更新输入框的值
        this.keywords = newValue;
        // 如果你希望输入框保持焦点并且光标在正确的位置，你可能需要使用 $nextTick
        setTimeout(() => {
          input.focus();
          input.setSelectionRange(startPos + text.length, startPos + text.length);
        }, 0);
      }
    },
    addSlide(item) {
      this.insertTextAtCursor(item)
    },
    focusSlide() {
      if (this.keywords) {
        this.slideFlag = true
      }
    },
    showSlide(val) {
      if (val) {
        this.slideFlag = true
      }
    },
    hideSlide() {
      this.slideFlag = false
    },
    // 查看原文
    viewOrigin(url) {
      window.open(url, '_blank')
    },
    // 获取政法热点排行
    async queryPolitics() {
      try {
        this.politicsLoading = true
        let res = await hotList({type: 4, count: 10})
        this.politicsLaw = res.data
      } finally {
        this.politicsLoading = false
      }
    },
    // 获取法院排行
    async queryCourt() {
      try {
        this.courtLoading = true
        let res = await hotList({type: 1, count: 10})
        this.courtData = res.data
      } finally {
        this.courtLoading = false
      }
    },
    // 获取检察排行
    async queryProsecutors() {
      try {
        this.prosecutorsLoading = true
        let res = await hotList({type: 2, count: 4})
        this.prosecutorsData = res.data
      } finally {
        this.prosecutorsLoading = false
      }
    },
    // 获取刑事事件排行
    async queryCase() {
      try {
        this.caseLoading = true
        let res = await hotList({type: 3, count: 4})
        this.caseData = res.data
      } finally {
        this.caseLoading = false
      }
    },
    // 平台- 微博热榜数据
    async platWeiboList() {
      try {
        this.weiboLoading = true
        let res = await hotRankList({type: 1, count: 10})
        this.realTimeData = res.data.realTime
        this.newsData = res.data.news
      } finally {
        this.weiboLoading = false
      }
    },
    // 平台- 抖音热榜
    async platDouyinList() {
      try {
        this.douyinLoading = true
        let res = await hotRankList({type: 2, count: 10})
        this.hotData = res.data.hot
        this.videoData = res.data.video
      } finally {
        this.douyinLoading = false
      }
    },
    // 平台- 百度热榜
    async platBaiduList() {
      try {
        this.baiduLoading = true
        let res = await hotRankList({type: 3, count: 10})
        this.opinionRealTimeData = res.data.realTime
        this.dayData = res.data.day
        this.weekData = res.data.week
      } finally {
        this.baiduLoading = false
      }
    },
    // 获取热词
    async queryWordList() {
      try {
        this.wordsLoading = true
        let res = await hotWordList({count: 15})
        this.wordsData = res.data
      } finally {
        this.wordsLoading = false
      }
    },
    // 搜索
    handleSearch() {
      this.hideSlide()
      if (this.keywords) {
        // 设置热词
        hotWordAdd({word: this.keywords}).then(() => {
          this.queryWordList()
        })
        const fullPath = this.$router.resolve({path: '/fullSearch/searchResult', query: {title: this.keywords}})
        window.open(fullPath.href, '_blank')
      } else {
        this.$message.error('请输入关键词进行搜索')
      }
    },
    // 热词点击
    setKeywords(word) {
      this.keywords = word
    },
    // 删除热词
    delKeywords(word) {
      this.$confirm('是否确认删除热词为"' + word + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return hotWordDelete({word: word});
      }).then(() => {
        this.queryWordList();
        this.msgSuccess("删除成功");
      })
    },
    // 获取相关推荐词
    async queryRecommendWordList() {
      try {
        this.recommendWordsLoading = true
        let res = await recommendWordListApi({count: 5})
        this.recommendWordsData = res.data
      } finally {
        this.recommendWordsLoading = false
      }
    },
    goSearchLog(){
      const routePath = this.$router.resolve({
        path: '/fullSearch/searchLog',
        query: {
          // startTime: this.queryParams.startTime,
        }
      })
      window.open(routePath.href, '_blank')
    },
  }
}
</script>
<style scoped lang="scss">
@import "./index.scss";
.search-log-btn{
  flex-shrink: 0;
  margin-left: 10px;
  cursor: pointer;
}
</style>
