import router from './router'
import store from './store'
import {Message} from 'element-ui'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import {getToken} from '@/utils/auth'

NProgress.configure({showSpinner: false})

const whiteList = ['/login', '/auth-redirect', '/bind', '/register', '/fullSearch',
  '/outReport', '/singleLogin', '/phoneDetail', '/wechatAlert', '/warnList', '/vsSingUp']

router.beforeEach((to, from, next) => {
  NProgress.start()
  document.title = to.meta.title ? store.state.user.titleName + '-' + to.meta.title : '舆情监测系统';
  if (getToken()) {
    /* has token*/
    if (to.path === '/login') {
      // token存在时，跳过登录页，直接进入模拟演练
      next({path: '/simulatedVS/index'})
      NProgress.done()
    } else if (to.path === '/singleLogin') {
      next()
    } else if (to.path === '/phoneDetail') {
      next()
    } else {
      if (store.getters.roles.length === 0) {
        // 判断当前用户是否已拉取完user_info信息
        store.dispatch('GetInfo').then(res => {
          // 拉取user_info
          const roles = res.roles
          store.dispatch('GenerateRoutes', {roles}).then(accessRoutes => {
            // 根据roles权限生成可访问的路由表
            router.addRoutes(accessRoutes) // 动态添加可访问路由表
            next({...to, replace: true}) // hack方法 确保addRoutes已完成
          })
        }).catch(err => {
          store.dispatch('LogOut').then(() => {
            next({path: '/'})
          })
        })
      } else {
        next()
      }
    }
  } else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next()
    } else {
      next(`/login?redirect=${to.fullPath}`) // 否则全部重定向到登录页
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})
