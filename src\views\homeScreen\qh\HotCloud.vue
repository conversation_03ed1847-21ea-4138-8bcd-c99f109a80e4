<template>
  <div class="ciyun" v-if="cloudData.length > 0">
    <div>
      <canvas id="myCanvas"></canvas>
      <div id="tags">
        <a
          id="word"
          href="javascript:void(0);"
          v-for="item in cloudData"
          :data-weight="item.value"
          :key="item.name"
        >{{ item.name }}</a>
      </div>
    </div>
  </div>
</template>

<script>
import 'echarts-wordcloud';

export default {
  props: {
    chartData: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      cloudData: [],
      wordData: {},
    }
  },
  watch: {
    chartData(newVal) {
      this.cloudData = this.chartData;
      this.$nextTick(() => {
        this.ciyun();
      });
    }
  },
  mounted() {
    // this.ciyun();
  },
  methods: {
    ciyun() {
      try {
        var i;
        var et = document.getElementById('tags').childNodes;
        var colors = ['#FF0000', '#ff9f1c', '#8338ec', '#3A86FF', '#2EC4B6', '#EF476F', '#00CDFF', '#B8DE3C', '#06d6a0', '#00a6fb', '#ff4000', '#ff5400', '#FFCF00', '#CC25A2', '#07BEB8', '#21DF00', '#FF70EB']; // 预设的颜色数组
        for (i in et) {
          if (et[i].nodeName == 'A') {
            var randomColorIndex = Math.floor(Math.random() * colors.length); // 获取随机索引
            et[i].style.color = colors[randomColorIndex]; // 应用颜色
            et[i].style.fontSize = (parseInt(Math.random() * 13) + 12) + 'px';
            et[i].style.fontWeight = 'bold';
          }
        }
        window.TagCanvas.Delete('myCanvas');
        window.TagCanvas.Start('myCanvas', 'tags', {
          textColour: null,
          zoom: 0.9,
          reverse: true,
          weight: true,
          minBrightness: 1,
          weightFrom: 'data-weight',
          weightMode: 'size',
          weightSize: 1,
          weightSizeMin: 12,
          weightSizeMax: 22,
          depth: 2.9,
          dragControl: true,
          maxSpeed: 0.01,
          minSpeed: 0.01,
          initial: [-0.2, 0]
        });
      } catch (e) {
      } finally {
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.ciyun {
  text-align: center;
  width: 100%;
  height: 100%;
}

#myCanvas {
  width: 100%;
  height: 100%;
}

#tags {
  width: 100%;
  height: 100%;
}

.loading {
  text-align: center;
  font-size: 20px;
  color: #333;
}
</style>
