<template>
  <div class="echarts">
    <div ref="DrillMap" v-loading="mapLoading" element-loading-background="rgba(18, 42, 96,0.6)" style="width: 100; height: 100%"></div>
    <div class="mapChoose">
      <span v-for="(item, index) in parentInfo" :key="item.code">
        <span class="title" @click="chooseArea(item, index)">{{
          item.cityName == "全国" ? "中国" : item.cityName
        }}</span>
        <span v-show="index + 1 != parentInfo.length" class="icon">-</span>
      </span>
    </div>
  </div>
</template>
    
<script>
import axios from 'axios'
import * as echarts from 'echarts'
// import resize from './mixins/resize'
// import { getStore } from '@/util/store'
import { getCountryMap } from '@/api/screen/index.js'
export default {
  name: 'DrillMap',
//   mixins: [resize],
  props: {
    chartParams: {
      type: Object,
      default: () => {}
    },
  },
  data() {
    return {
      myChart: null,
      geoJson: {
        features: []
      },
      parentInfo: [],
      mapDataList: [],
      mapLoading: false,
      nowParams: {},
      areaInfo: {
        adcode: '640000', // 630000 青海 
        adName: '宁夏'
      } 
    }
  },
  watch: {
    nowParams() {
      this.$nextTick(() => {
        this.getGeoJson(this.nowParams.areaCode)
      })
    }
  },
  mounted() {
    this.initMap(this.areaInfo) //地图初始化区域
  },
  methods: {
    initMap({ adcode, adName }) {
      // this.parentInfo = [{
      //   cityName: adName,
      //   code: adcode
      // }]
      this.parentInfo = [
        {
          cityName: '全国',
          code: '100000'
        },
        {
          cityName: '宁夏',
          code: '640000'
        }
      ]
      this.getGeoJson(adcode) //地图初始化区域，和parentInfo一致
    },
    getGeoJson(adcode) {
      const that = this

      // 使用本地区域边界数据
      const areaCode = adcode
      axios.get(`https://oss.boryou.com/oss/geo/${areaCode}`)
        .then((res) => {
          console.log('axios request success', res)
          if (res.status === 200) {
            const cleanGeoJson = res.data
            that.geoJson = cleanGeoJson
            console.log('that.geoJson', that.geoJson)
          } else {
            console.error('获取区域边界失败:', res.msg)
            // 接口获取不到geoJSON，用上一级数据过滤出点击的区县数据
            that.geoJson.features = that.geoJson.features.filter(
              (item) => item.properties.adcode == adcode
            )
            // 初始化时就为区县级，无上一级数据进行过滤，获取上一级行政区划代码
            if (that.geoJson.features.length === 0) {
              const parentCode = this.getParentAreaCode(adcode)
              this.getGeoJson(parentCode)
            }
          }
          that.getMapData(areaCode)
        })
        .catch((error) => {
          console.error('网络错误:', error)
        })


      // 使用高德区域边界数据
      // eslint-disable-next-line no-undef
      // AMapUI.loadUI(['geo/DistrictExplorer'], (DistrictExplorer) => {
      //   var districtExplorer = new DistrictExplorer()
      //   districtExplorer.loadAreaNode(adcode, function(error, areaNode) {
      //     if (error) {
      //       console.error(error)
      //       return
      //     }
      //     const Json = areaNode.getSubFeatures()
      //     if (Json.length > 0) {
      //       that.geoJson.features = Json
      //     } else if (Json.length === 0) {
      //       that.geoJson.features = that.geoJson.features.filter(
      //         (item) => item.properties.adcode == adcode
      //       )
      //       if (that.geoJson.features.length === 0) {
      //         that.geoJson.features = [areaNode.getParentFeature()]
      //       }
      //     }
      //     that.getMapData()
      //   })
      // })
    },
    getParentAreaCode(areaCode) {
      areaCode = String(areaCode)
      if (typeof areaCode !== 'string' || areaCode.length !== 6) {
        return new Error('行政区划代码必须是六位数字字符串')
      }
      // 省级行政区划代码，后四位为 '0000'
      if (areaCode.endsWith('0000')) {
        return '100000' // 省级行政区划没有上级
      }
      // 市级行政区划代码，后两位为 '00'
      if (areaCode.endsWith('00')) {
        return areaCode.slice(0, 2) + '0000' // 返回省级行政区划代码
      }
      // 区县级行政区划代码，后两位不为 '00'
      return areaCode.slice(0, 4) + '00' // 返回市级行政区划代码
    },
    //获取数据
    getMapData(areaCode) {
      // console.log(this.parentInfo[this.parentInfo.length - 1], 'parentInfo') //{cityName: "合肥市",code: 340100}
      // console.log(this.geoJson, 'this.geoJson')

      this.mapLoading = true
      const params = {
        ...this.nowParams,
        areaCode: areaCode,
      }
      getCountryMap(params).then(res => {
        this.mapLoading = false
        if (res.code == 200) {
          const mapData = res.data.map((item) => {
            return {
              name: item?.areaName || '',
              cityCode: item?.areaId || 0,
              value: item?.issueArticleNum || 0,
            }
          })
          // mapData = mapData.sort(function(a, b) {
          //   return b.accountNum - a.accountNum
          // })
          this.mapDataList = mapData

          this.$emit('getTreeData', res.data)
          let pointData = res.data.map((item) => {
          // console.log(item, item.pointValue.push(item.issueArticleNum), '*********')
            return {
              name: item.areaName,
              value: [item.pointValue[0], item.pointValue[1], item.issueArticleNum],
              cityCode: item.areaId,
            }
          })
          pointData = pointData.sort(function(a, b) {
            return b.value[2] - a.value[2]
          })
          this.initEcharts(mapData, pointData)
        } else {
          this.initEcharts([], [])
        }
      })
    },
    initEcharts(mapData, pointData) {
      var min = pointData[pointData.length - 1]?.value[2] || 0
      var max = pointData[0]?.value[2] || 0
      if (pointData.length === 1) {
        min = 0
      }

      const _this = this
      this.myChart = echarts.init(this.$refs.DrillMap)

      const myChart = echarts.init(this.$refs.DrillMap)
      echarts.registerMap('Map', this.geoJson) //注册
      // myChart.clear();

      clearInterval(_this.timer)
      myChart.showLoading()
      let index = -1
      _this.timer = setInterval(function() {
        myChart.dispatchAction({ // 隐藏提示框
          type: 'hideTip',
          seriesIndex: 0,
          dataIndex: index
        })
        myChart.dispatchAction({ // 取消高亮指定的数据图形
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: index
        })
        index++
        if (index > mapData.length - 1) {
          index = 0
        }
        myChart.dispatchAction({ // 显示提示框
          type: 'showTip',
          seriesIndex: 0,
          dataIndex: index
        })
        if (mapData.length > 1) {
          myChart.dispatchAction({ // 高亮指定的数据图形
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: index
          }) 
        }
      }, 2000)

      myChart.on('mousemove', function(e) { // 鼠标移入静止播放
        clearInterval(_this.timer)
        myChart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0
        })
        myChart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: e.dataIndex
        })
        myChart.dispatchAction({
          type: 'showTip',
          seriesIndex: 0,
          dataIndex: e.dataIndex
        })
      })

      myChart.on('mouseout', function(e) { 
        clearInterval(_this.timer)
        myChart.dispatchAction({ //鼠标移出后先把上次的高亮取消
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: e.dataIndex
        })
        _this.timer = setInterval(function() {
          myChart.dispatchAction({ // 隐藏提示框
            type: 'hideTip',
            seriesIndex: 0,
            dataIndex: index
          })
          myChart.dispatchAction({ // 显示提示框
            type: 'showTip',
            seriesIndex: 0,
            dataIndex: index + 1
          })
          myChart.dispatchAction({ // 取消高亮指定的数据图形
            type: 'downplay',
            seriesIndex: 0,
            dataIndex: index
          })
          if (mapData.length > 1) {
            myChart.dispatchAction({ // 高亮指定的数据图形
              type: 'highlight',
              seriesIndex: 0,
              dataIndex: index + 1
            }) 
          }
          index++
          if (index > mapData.length - 1) {
            index = -1
          }
        }, 2000)
      })
      myChart.hideLoading()

      myChart.off('click')
      myChart.on('click', (params) => {
      console.log('click:>> ', params);
        
        // 直辖市判断
        const newCode = this.parentInfo[this.parentInfo.length - 1].code;
        const municipalityCodes = ['110000', '310000', '120000', '500000'];
        if (municipalityCodes.includes(newCode)) {
            return;
        }
        const pointObj = params.data || null
        if(this.parentInfo.length < 3 && pointObj){
          this.$emit('getCityCode',pointObj)
        }
        clearInterval(_this.timer)

        if (
          !params.data?.cityCode ||
          this.parentInfo[this.parentInfo.length - 1].code ==
          params.data.cityCode
        ) {
          return
        }
         if (this.parentInfo.length == 3) {
          // 2市 3县
          return
        }
        
        if (
          this.parentInfo[this.parentInfo.length - 1].code ==
          params.data.code
        ) {
          return
        }
        const data = params.data
        this.parentInfo.push({
          cityName: data.name,
          code: data.cityCode
        })
        this.getGeoJson(data.cityCode)
      })

      this.myChart.setOption(
        {
          tooltip: {
            trigger: 'item',
            backgroundColor: 'transparent',
            borderColor: 'transparent',
            borderWidth: 0,
            padding: 0, // 提示框浮层内边距

            // extraCssText: 'z-index:100;color:#fff;',
            // confine: true, //是否将 tooltip 框限制在图表的区域内
            position: (point, params, dom, rect, size) => {
              //   console.log(point, params, dom, rect, size, 'point, params, dom, rect, size')
              return [point[0] - 70, point[1] - 72]
            },
            formatter: (p) => {
              const numValue = p.value || 0
              const txtCon = `
                <div class='echarts_tooltip'>
                  <div class='tooltipMain'>
                    <div class='tooltipTitle'>${p.name} ${numValue}</div>
                  </div>
                </div>`
              // console.log(p, 'pppppppp')
              return txtCon
            }
          },
          title: {
            show: true,
            left: 'center',
            top: '15',
            textStyle: {
              color: 'rgb(179, 239, 255)',
              fontSize: '0.16rem'
            }
          },
          toolbox: {
            feature: {
              restore: {
                show: false
              },
              dataZoom: {
                show: false
              },
              magicType: {
                show: false
              }
            },
            iconStyle: {
              // normal: {
              borderColor: '#53D9FF'
              // }
            },
            top: 15,
            right: 35
          },
          geo: {
            //散点图地图
            map: 'Map',
            zoom: 1,
            roam: true,
            itemStyle: {
              // normal: {
              areaColor: '#86fdff',
              shadowColor: '#86fdff',
              shadowOffsetX: 5,
              shadowOffsetY: 3,
              // },
              emphasis: {
                areaColor: '#8dd7fc'
              }
            }
          },
          visualMap: {
            //地图左下角柱状标识
            show: false,
            // min: min,
            // max: max,
            align: 'left',
            left: '20%',
            bottom: '5%',
            calculable: true,
            seriesIndex: [0],
            inRange: {
              color: ['#105389', '#3a8abc', '#0D96F1']
            }
          },
          series: [
            {
              name: '地图',
              type: 'map',
              map: 'Map',
              selectedMode: 'none', // 禁用选中模式
              roam: true, //是否可缩放
              zoom: 1, //缩放比例
              data: mapData,
              geoIndex: 0,
              label: {
                // normal: {
                show: true,
                color: '#ffffff', //省份标签字体颜色
                formatter: (p) => {
                  switch (p.name) {
                    case '内蒙古自治区':
                      p.name = '内蒙古'
                      break
                    case '西藏自治区':
                      p.name = '西藏'
                      break
                    case '新疆维吾尔自治区':
                      p.name = '新疆'
                      break
                    case '宁夏回族自治区':
                      p.name = '宁夏'
                      break
                    case '广西壮族自治区':
                      p.name = '广西'
                      break
                    case '香港特别行政区':
                      p.name = '香港'
                      break
                    case '澳门特别行政区':
                      p.name = '澳门'
                      break
                    default:
                      break
                  }
                  return p.name
                  // }
                },
                emphasis: {
                  show: true,
                  color: '#ffffff' //hover后的地区文字颜色
                }
              },
              itemStyle: {
                // normal: {
                areaColor: '#24CFF4', //地图颜色
                borderColor: '#53D9FF',
                borderWidth: 1.8,
                //   shadowBlur: 10,
                //   shadowColor: 'rgb(58,115,192)',
                //   shadowOffsetX: 7,
                //   shadowOffsetY: 6
                // },
                emphasis: {
                  areaColor: '#94e4ec',
                  borderWidth: 1.8,
                  shadowBlur: 25
                }
              }
            },
            {
              name: '散点',
              type: 'effectScatter',
              coordinateSystem: 'geo',
              showEffectOn: 'render',
              rippleEffect: {
                period: 15,
                scale: 4,
                brushType: 'fill'
              },
              emphasis: {
                scale: true
              },
              itemStyle: {
                // normal: {
                color: '#FFAD04', //热点颜色
                shadowBlur: 10,
                shadowColor: '#333'
                // }
              },
              data: pointData,
              symbolSize: function(val) {
                if (val[2] == 0) {
                  return 0
                }
                const maxSize = 20
                const minSize = 5
                // 将 value 映射到 [0, 1] 范围内
                const normalizedValue = (val[2] - min) / (max - min)
                // 使用映射后的值来计算对应的尺寸范围
                const sizeRange = maxSize - minSize
                const mappedSize = minSize + normalizedValue * sizeRange
                return mappedSize
              }
            }
          ]
        },
        true
      )
    },

    //选择切换市县
    chooseArea(val, index) {
      console.log('val :>> ', val);
       const pointObj = {
        cityCode: val.code,
        name: val.cityName
       }
      this.$emit('getCityCode',pointObj)
      // if (this.parentInfo.length === index + 1) {
      //   return
      // }
      // if(val.code == '100000'){

      // }
      this.parentInfo.splice(index + 1)
      // this.getGeoJson(this.parentInfo[this.parentInfo.length - 1].code)
      this.getGeoJson(val.code)
    }
  }
}
</script>
<style lang="scss" scoped>
.echarts {
  width: 100%;
  height: 100%;
  position: relative;
  //   background: url("../assets/bg1.jpg") no-repeat;
  background: transparent;
  background-size: 100% 100%;

  ::v-deep div {
    box-shadow: none !important;
  }
}

.mapChoose {
  position: absolute;
  left: 0.1rem;
  top: 0.05rem;
  // color: #eee;
  background: linear-gradient(to bottom, #95ddff 30%, #EFFCFE 70%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;

  font-size: 0.2rem;
  color: #fff;
  font-weight: bold;
  // font-style: italic;
  display: inline-block;
  transform: skew(-20deg, 0deg);


  .title {
    // padding: 0.04rem;
    // border-top: 1px solid rgba(147, 235, 248, 0.8);
    // border-bottom: 1px solid rgba(147, 235, 248, 0.8);
    cursor: pointer;

  }

  .icon {
    // font-family: "simsun";
    // font-size: 25px;
    // margin: 0 0.05rem;
  }
}

::v-deep .echarts_tooltip {
  font-size: 0.12rem;
  background-image: url('../../../assets/screen/tooltipPop.png');
  background-size: 100% 100%;
  max-width: 300px;
  // height: 0.5rem;

  .tooltipMain {
    padding: 0.18rem;

    .tooltipTitle {
      color: #04d9ee
    }

    .numItem {
      color: #fff;
      margin-left: 0.1rem;
    }
  }
}
</style>
